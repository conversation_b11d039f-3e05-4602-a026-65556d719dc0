// eslint-disable-next-line import/no-extraneous-dependencies
import { apiResponseCodes, planIntervals } from "easyflow-enums";

import lodash from "lodash";
import configs from "../../configs";

const { isEmpty } = lodash;

/**
 * @param {any} err
 * @returns {Promise<string | null>}
 */
const getApiError = async (err) => {
  try {
    const { status } = err;
    const { message, errors } = await err.json();

    if (status === apiResponseCodes.VALIDATION_ERROR && !isEmpty(errors)) {
      return Object.values(errors)[0];
    }

    if (status >= 400 && message.length > 0) {
      return message;
    }
  } catch (e) {
    return null;
  }
  return null;
};

const hasDiscount = (plan) => plan.discount > 0 && plan.interval !== planIntervals.USAGE;
const getFinalPrice = (plan) => (plan.interval === planIntervals.USAGE ? plan.price : plan.final_price).toFixed(2);

const checkoutModalId = "checkout-modal";

/**
 * @param {string} path
 * @param {string} [target]
 */
const appRedirectToPath = (path, target = "_parent") => {
  window.open(path, target);
};

/**
 *
 * @param {string} appHandle
 * @param {string} path
 */
const appNavigateToPath = (appHandle, path, target = "_self") => {
  window.open(`shopify://admin/apps/${appHandle}${path}`, target);
};

const getAppHandleFromEnv = () => (configs.appHandle ? configs.appHandle : "/");
export {
  appNavigateToPath,
  appRedirectToPath,
  checkoutModalId,
  getApiError,
  getAppHandleFromEnv,
  getFinalPrice,
  hasDiscount,
};
