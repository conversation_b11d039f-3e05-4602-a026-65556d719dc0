import configs from "../configs";

const useThirdPartyScripts = () => ({
  loadClarityScript() {
    const clarityScript = document.querySelector('script[src="https://www.clarity.ms/tag/q1g9iaxw8j"]');
    // Load Clarity script if enabled
    if (!clarityScript && configs.loadClarityScript) {
      (function (c, l, a, r, i, t, y) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
      })(window, document, "clarity", "script", "q1g9iaxw8j");
    }
  },
  // Function to remove the Clarity script
  removeClarityScript() {
    const script = document.querySelector('script[src="https://www.clarity.ms/tag/q1g9iaxw8j"]');
    if (script) {
      script.remove();
      console.log("Clarity script removed successfully.");
    } else {
      console.log("Clarity script not found.");
    }
  },
});

export default useThirdPartyScripts;
