// import "@shopify/polaris/build/esm/styles.css";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

import * as Sentry from "@sentry/browser";
// eslint-disable-next-line import/no-extraneous-dependencies
import { onCLS, onINP, onLCP } from "web-vitals";
import Base from "./Base";
import configs from "./configs";
// import "./style/custom.css";

const shopId = new URLSearchParams(window.location.search).get("shop");

// Initialize Sentry replays
Sentry.init({
  dsn: configs.sentryDNS,
  replaysSessionSampleRate: 1,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.replayIntegration({ maskAllText: false, blockAllMedia: true }),
  ],
  ...(shopId ? { initialScope: { user: { id: shopId } } } : {}),
});

const container = document.getElementById("app");
if (!container) {
  throw new Error("React root element is not found.");
}

onLCP((metric) => console.log("LCP: ", { ...metric }), { reportAllChanges: true });
onCLS((metric) => console.log("CLS: ", { ...metric }), { reportAllChanges: true });
onINP((metric) => console.log("INP: ", { ...metric }), { reportAllChanges: true });

createRoot(container).render(
  <StrictMode>
    <Base />
  </StrictMode>
);
