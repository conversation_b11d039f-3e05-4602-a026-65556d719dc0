/* eslint-disable import/prefer-default-export */
export const settingsPageSkeleton = `<div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--isSingleRow Polaris-Page-Header--noBreadcrumbs Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__TitleWrapper Polaris-Page-Header__TitleWrapperExpand"><div class="Polaris-Header-Title__TitleWrapper"><h1 class="Polaris-Header-Title"><span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">Settings</span></h1></div></div></div></div></div><div class=""><ui-save-bar id="easyflow-save-bar"><button type="button" variant="primary">Save</button><button type="button">Discard</button></ui-save-bar><div class="settingspage-container"><div class="Polaris-Layout"><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Customize how price add-ons work</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Make sure to enable the EasyFlow App Embed for these settings to work properly. If the app embed is enabled the add-on product will be removed from the cart if the base product is removed.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 250px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 100px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 100px;"></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Customize how add-on prices are displayed</h2></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 250px;"></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Customize system text</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Enable the EasyFlow App Embed if you want the add-on product to be removed from the cart if the base product is removed.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 350px;"></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">(Advanced) Customize app embed behavior</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div></div></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 350px;"></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 350px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 350px;"></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 350px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 200px;"></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 15px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 400px;"></div></div></div></div></div></div></div></div></div></div></div></div></div>`;
