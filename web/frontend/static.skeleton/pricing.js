/* eslint-disable import/prefer-default-export */
export const pricingSkeletonPage = /* html */ `<div class="Polaris-Page Polaris-Page--narrowWidth">
  <div
    class="Polaris-Box"
    style="
      --pc-box-padding-block-start-xs: var(--p-space-400);
      --pc-box-padding-block-start-md: var(--p-space-600);
      --pc-box-padding-block-end-xs: var(--p-space-400);
      --pc-box-padding-block-end-md: var(--p-space-600);
      --pc-box-padding-inline-start-xs: var(--p-space-400);
      --pc-box-padding-inline-start-sm: var(--p-space-0);
      --pc-box-padding-inline-end-xs: var(--p-space-400);
      --pc-box-padding-inline-end-sm: var(--p-space-0);
      position: relative;
    "
  >
    <div role="status"><p class="Polaris-Text--root <PERSON><PERSON>-Text--visuallyHidden">Choose the perfect plan for your store. This page is ready</p></div>
    <div class="Polaris-Page-Header--isSingleRow Polaris-Page-Header--longTitle">
      <div class="Polaris-Page-Header__Row">
        <div class="Polaris-Page-Header__BreadcrumbWrapper">
          <div class="Polaris-Box Polaris-Box--printHidden" style="--pc-box-max-width: 100%; --pc-box-padding-inline-end-xs: var(--p-space-100)">
            <button
              class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconOnly"
              aria-label="Back"
              type="button"
            >
              <span class="Polaris-Button__Icon"
                ><span class="Polaris-Icon"
                  ><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true">
                    <path
                      fill-rule="evenodd"
                      d="M16.5 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 1 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"
                    ></path></svg></span
              ></span>
            </button>
          </div>
        </div>
        <div class="Polaris-Page-Header__TitleWrapper Polaris-Page-Header__TitleWrapperExpand">
          <div class="Polaris-Header-Title__TitleWrapper">
            <h1 class="Polaris-Header-Title Polaris-Header-Title__TitleWithSubtitle">
              <span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">Choose the perfect plan for your store</span>
            </h1>
          </div>
          <div class="Polaris-Header-Title__SubTitle">
            <p class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--subdued">Explore our flexible pricing options and choose the one that fits your needs</p>
          </div>
        </div>
        <div class="Polaris-Page-Header__RightAlign"></div>
      </div>
    </div>
  </div>
  <div class="">
    <div
      class="Polaris-Box"
      style=""
    >
      <div class="subscription-wrapper">
        <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)">
          <div class="pro_plans">
            <div class="Polaris-Grid" style="--pc-grid-columns-xs: 1; --pc-grid-columns-sm: 1; --pc-grid-columns-md: 2; --pc-grid-columns-lg: 2">
              <div style="display: flex">
                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: '';
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-0);
                        --pc-box-padding-block-end-xs: var(--p-space-0);
                        --pc-box-padding-inline-start-xs: var(--p-space-0);
                        --pc-box-padding-inline-end-xs: var(--p-space-0);
                      "
                    >
                      <div class="Polaris-Box">
                        <div class="Polaris-BlockStack" style="--pc-block-stack-order: column">
                          <div
                            class="Polaris-Box"
                            style="
                              --pc-box-padding-block-start-xs: var(--p-space-400);
                              --pc-box-padding-block-end-xs: var(--p-space-400);
                              --pc-box-padding-inline-start-xs: var(--p-space-400);
                              --pc-box-padding-inline-end-xs: var(--p-space-400);
                            "
                          >
                            <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)">
                              <div
                                class="Polaris-InlineStack"
                                style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-flex-direction-xs: row"
                              >
                                <div
                                  class="Polaris-BlockStack"
                                  style="--pc-block-stack-inline-align: start; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-100)"
                                >
                                  <div
                                    class="Polaris-InlineStack"
                                    style="
                                      --pc-inline-stack-align: start;
                                      --pc-inline-stack-wrap: wrap;
                                      --pc-inline-stack-gap-xs: var(--p-space-200);
                                      --pc-inline-stack-flex-direction-xs: row;
                                    "
                                  >
                                    <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px"></div></div>
                                    <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                  </div>
                                  <div
                                    class="Polaris-BlockStack"
                                    style="--pc-block-stack-align: start; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200)"
                                  >
                                    <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                  </div>
                                </div>
                                <div class="Polaris-BlockStack" style="--pc-block-stack-inline-align: end; --pc-block-stack-order: column">
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)" />
                          <div class="plan_card_feature">
                            <div
                              class="Polaris-Box"
                              style="
                                --pc-box-padding-block-start-xs: var(--p-space-400);
                                --pc-box-padding-block-end-xs: var(--p-space-400);
                                --pc-box-padding-inline-start-xs: var(--p-space-400);
                                --pc-box-padding-inline-end-xs: var(--p-space-400);
                              "
                            >
                              <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-300)">
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-block-align: center;
                                    --pc-inline-stack-wrap: nowrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)" />
                          <div class="plan_card_footer">
                            <div
                              class="Polaris-Box"
                              style="
                                --pc-box-padding-block-start-xs: var(--p-space-400);
                                --pc-box-padding-block-end-xs: var(--p-space-400);
                                --pc-box-padding-inline-start-xs: var(--p-space-400);
                                --pc-box-padding-inline-end-xs: var(--p-space-400);
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 250px"></div></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="visionary_plan">
                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: '';
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-0);
                        --pc-box-padding-block-end-xs: var(--p-space-0);
                        --pc-box-padding-inline-start-xs: var(--p-space-0);
                        --pc-box-padding-inline-end-xs: var(--p-space-0);
                      "
                    >
                      <div class="Polaris-BlockStack" style="--pc-block-stack-order: column">
                        <div
                          class="Polaris-Box"
                          style="
                            --pc-box-padding-block-start-xs: var(--p-space-400);
                            --pc-box-padding-block-end-xs: var(--p-space-400);
                            --pc-box-padding-inline-start-xs: var(--p-space-400);
                            --pc-box-padding-inline-end-xs: var(--p-space-400);
                          "
                        >
                          <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)">
                            <div
                              class="Polaris-InlineStack"
                              style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-flex-direction-xs: row"
                            >
                              <div
                                class="Polaris-BlockStack"
                                style="--pc-block-stack-inline-align: start; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-100)"
                              >
                                <div
                                  class="Polaris-InlineStack"
                                  style="
                                    --pc-inline-stack-align: start;
                                    --pc-inline-stack-wrap: wrap;
                                    --pc-inline-stack-gap-xs: var(--p-space-200);
                                    --pc-inline-stack-flex-direction-xs: row;
                                  "
                                >
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px"></div></div>
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                </div>
                                <div class="Polaris-BlockStack" style="--pc-block-stack-align: start; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200)">
                                  <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                </div>
                              </div>
                              <div class="Polaris-BlockStack" style="--pc-block-stack-inline-align: end; --pc-block-stack-order: column">
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 100px"></div></div>
                                <div
                                  class="Polaris-InlineStack"
                                  style="--pc-inline-stack-block-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row"
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)" />
                        <div class="plan_card_feature">
                          <div
                            class="Polaris-Box"
                            style="
                              --pc-box-padding-block-start-xs: var(--p-space-400);
                              --pc-box-padding-block-end-xs: var(--p-space-400);
                              --pc-box-padding-inline-start-xs: var(--p-space-400);
                              --pc-box-padding-inline-end-xs: var(--p-space-400);
                            "
                          >
                            <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-300)">
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                              <div
                                class="Polaris-InlineStack"
                                style="
                                  --pc-inline-stack-block-align: center;
                                  --pc-inline-stack-wrap: nowrap;
                                  --pc-inline-stack-gap-xs: var(--p-space-200);
                                  --pc-inline-stack-flex-direction-xs: row;
                                "
                              >
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                                <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)" />
                        <div class="plan_card_footer">
                          <div
                            class="Polaris-Box"
                            style="
                              --pc-box-padding-block-start-xs: var(--p-space-400);
                              --pc-box-padding-block-end-xs: var(--p-space-400);
                              --pc-box-padding-inline-start-xs: var(--p-space-400);
                              --pc-box-padding-inline-end-xs: var(--p-space-400);
                            "
                          >
                            <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 250px"></div></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="Polaris-Box">
            <div
              class="Polaris-InlineStack"
              style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row"
            >
              <div class="Polaris-Box" style="--pc-box-width: 40%">
                <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border)" />
              </div>
              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px"></div></div>
              <div class="Polaris-Box" style="--pc-box-width: 40%">
                <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border)" />
              </div>
            </div>
          </div>
          <div class="free-plan">
            <div
              class="Polaris-ShadowBevel"
              style="
                --pc-shadow-bevel-z-index: 32;
                --pc-shadow-bevel-content-xs: '';
                --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
              "
            >
              <div
                class="Polaris-Box"
                style="
                  --pc-box-background: var(--p-color-bg-surface);
                  --pc-box-min-height: 100%;
                  --pc-box-overflow-x: clip;
                  --pc-box-overflow-y: clip;
                  --pc-box-padding-block-start-xs: var(--p-space-0);
                  --pc-box-padding-block-end-xs: var(--p-space-0);
                  --pc-box-padding-inline-start-xs: var(--p-space-0);
                  --pc-box-padding-inline-end-xs: var(--p-space-0);
                "
              >
                <div class="Polaris-BlockStack" style="--pc-block-stack-order: column">
                  <div
                    class="Polaris-Box"
                    style="
                      --pc-box-border-start-end-radius: var(--p-border-radius-050);
                      --pc-box-padding-block-start-xs: var(--p-space-400);
                      --pc-box-padding-block-end-xs: var(--p-space-400);
                      --pc-box-padding-inline-start-xs: var(--p-space-400);
                      --pc-box-padding-inline-end-xs: var(--p-space-400);
                    "
                  >
                    <div
                      class="Polaris-InlineStack"
                      style="--pc-inline-stack-align: space-between; --pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row"
                    >
                      <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-100)">
                        <div
                          class="Polaris-InlineStack"
                          style="
                            --pc-inline-stack-align: start;
                            --pc-inline-stack-wrap: wrap;
                            --pc-inline-stack-gap-xs: var(--p-space-200);
                            --pc-inline-stack-flex-direction-xs: row;
                          "
                        >
                          <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px"></div></div>
                          <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px"></div></div>
                        </div>
                        <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 180px"></div></div>
                      </div>
                      <div class="Polaris-BlockStack" style="--pc-block-stack-align: end; --pc-block-stack-order: column">
                        <div
                          class="Polaris-InlineStack"
                          style="
                            --pc-inline-stack-block-align: center;
                            --pc-inline-stack-wrap: wrap;
                            --pc-inline-stack-gap-xs: var(--p-space-400);
                            --pc-inline-stack-flex-direction-xs: row;
                          "
                        >
                          <div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row">
                            <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 90px"></div></div>
                          </div>
                          <div class="Polaris-Box">
                            <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 60px"></div></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)" />
                  <div class="plan_card_feature">
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-padding-block-start-xs: var(--p-space-400);
                        --pc-box-padding-block-end-xs: var(--p-space-400);
                        --pc-box-padding-inline-start-xs: var(--p-space-400);
                        --pc-box-padding-inline-end-xs: var(--p-space-400);
                      "
                    >
                      <div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-300)">
                        <div class="Polaris-Box">
                          <div
                            class="Polaris-InlineGrid"
                            style="
                              --pc-inline-grid-grid-template-columns-xs: repeat(1, minmax(0, 1fr));
                              --pc-inline-grid-grid-template-columns-sm: repeat(1, minmax(0, 1fr));
                              --pc-inline-grid-grid-template-columns-md: repeat(2, minmax(0, 1fr));
                              --pc-inline-grid-grid-template-columns-lg: repeat(2, minmax(0, 1fr));
                              --pc-inline-grid-grid-template-columns-xl: repeat(2, minmax(0, 1fr));
                              --pc-inline-grid-gap-xs: var(--p-space-100);
                            "
                          >
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: nowrap;
                                --pc-inline-stack-gap-xs: var(--p-space-200);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 20px"></div></div>
                              <div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px"></div></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
