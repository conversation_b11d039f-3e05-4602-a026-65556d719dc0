// eslint-disable-next-line import/prefer-default-export
export const homepageSkeletonDiv = `
<div class="Polaris-Frame" data-polaris-layer="true"><div class="Polaris-Frame__Skip"><a href="#AppFrameMain"><span class="Polaris-Text--root Polaris-Text--bodyLg Polaris-Text--medium">Skip to content</span></a></div><div class="Polaris-Frame__ContextualSaveBar Polaris-Frame-CSSAnimation--startFade"><div class="Polaris-Frame-ContextualSaveBar"><div class="Polaris-Frame-ContextualSaveBar__LogoContainer" style="width: 104px;"></div><div class="Polaris-Frame-ContextualSaveBar__Contents"><div class="Polaris-Frame-ContextualSaveBar__MessageContainer"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg></span></div><div class="Polaris-Frame-ContextualSaveBar__ActionContainer"><div class="Polaris-LegacyStack Polaris-LegacyStack--spacingTight Polaris-LegacyStack--noWrap"></div></div></div></div></div><main class="Polaris-Frame__Main" id="AppFrameMain" data-has-global-ribbon="false"><div class="Polaris-Frame__Content"><div class="homepage-container"><div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--noBreadcrumbs Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__TitleWrapper"><div class="Polaris-Header-Title__TitleWrapper"><h1 class="Polaris-Header-Title"><span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">EasyFlow Product Options</span></h1></div></div><div class="Polaris-Page-Header__RightAlign"><div><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter" type="button" tabindex="0" aria-controls=":r1:" aria-owns=":r1:" aria-expanded="false" data-state="closed"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Create option</span></button></div><div class="Polaris-Page-Header__PrimaryActionWrapper"><div class="Polaris-Box Polaris-Box--printHidden"><span class=""><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantPrimary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter" type="button" tabindex="0" aria-describedby=":r3:" data-polaris-tooltip-activator="true"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Create option set</span></button></span></div></div></div></div></div></div><div class=""><div class="Polaris-Box"><div class="homepage-static-loader"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 40px;"></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px;"></div></div><div class="Polaris-InlineGrid" style="--pc-inline-grid-grid-template-columns-xs: repeat(3, minmax(0, 1fr)); --pc-inline-grid-gap-xs: var(--p-space-200);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 150px; --pc-box-min-width: 200px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 180px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 150px; --pc-box-min-width: 200px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 180px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 90px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 90px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 180px;"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 180px; --pc-box-min-width: 200px;">&nbsp;</div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 500px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 40px;"></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px;"></div></div><div class="Polaris-InlineGrid" style="--pc-inline-grid-grid-template-columns-xs: repeat(3, minmax(0, 1fr)); --pc-inline-grid-gap-xs: var(--p-space-200);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></main></div>`;
