import React from "react";

const HtmlOptionIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5359_14099)">
      <path
        d="M5.83333 6.6665L2.5 9.99984L5.83333 13.3332"
        stroke="#8A8A8A"
        stroke-width="1.1"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.1641 6.6665L17.4974 9.99984L14.1641 13.3332"
        stroke="#8A8A8A"
        stroke-width="1.1"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.6693 3.3335L8.33594 16.6668"
        stroke="#8A8A8A"
        stroke-width="1.1"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_5359_14099">
        <rect
          width="20"
          height="20"
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>
);

export default HtmlOptionIcon;
