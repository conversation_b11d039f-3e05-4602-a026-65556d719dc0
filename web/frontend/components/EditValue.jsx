// @ts-ignore
import { arrayOf, bool, func, object, string } from "prop-types";
import { useState } from "react";

import { BlockStack, Box, Button, Checkbox, Icon, InlineStack, Label, TextField, Tooltip } from "@shopify/polaris";
import { DeleteIcon, DragHandleIcon, DuplicateIcon, LockFilledIcon } from "@shopify/polaris-icons";

// eslint-disable-next-line import/no-relative-packages
import { useNavigate } from "react-router-dom";
import CodeOnlyEditor from "./CodeOnlyEditor";
import ColorPicker from "./ColorPicker";
import HelpTooltip from "./HelpTooltip";
import ImageUpload from "./ImageUpload";
import ProductPicker from "./ProductPicker";

export function generateOptionValueElementId() {
  return `option-value-${Math.random().toString(36).substring(2, 7)}`;
}

/**
 * @param {{
 *   option: ProductOptionInput,
 *   value: Exclude<ProductOptionInput['values'], undefined>[number],
 *   onValueChange: (
 *     newValue: Partial<Exclude<ProductOptionInput['values'], undefined>[number]> | undefined,
 *   ) => void,
 *   setOption: (
 *     setter: (newOption: ProductOptionInput, valueIndex: number) => ProductOptionInput
 *   ) => void,
 *   isSingleValueType: boolean,
 *   isSingleSelectType: boolean,
 *   isAddonsAvailable: boolean,
 *   currency: string | undefined,
 *   errors: string[],
 *   isStaticType: boolean,
 * }} props
 */
export default function EditValue({
  option,
  value,
  onValueChange,
  setOption,
  isSingleValueType,
  isSingleSelectType,
  isAddonsAvailable,
  currency,
  errors = [],
  isStaticType,
}) {
  const navigate = useNavigate();
  const [isShowMoreOptions, setShowMoreOptions] = useState(false);

  const imageUpload = (
    <ImageUpload
      key="image-upload"
      imageFile={value.imageFile}
      imageURL={value.image?.url}
      onImageFileChange={(newImageFile) => onValueChange({ imageFile: newImageFile })}
    />
  );

  return (
    <div
      className="option-drag-div"
      style={{ marginBottom: !isAddonsAvailable && option.type === "Dropdown" ? "1.5rem" : undefined }}
    >
      <BlockStack gap="400">
        {option.type === "HTML" ? (
          <CodeOnlyEditor
            htmlContent={value.htmlContent || ""}
            onHtmlContentChange={(newHtmlContent) => onValueChange({ htmlContent: newHtmlContent })}
          />
        ) : (
          <InlineStack
            align="space-between"
            blockAlign="center"
            gap={!isSingleValueType ? "200" : undefined}
            wrap={false}
          >
            {!isSingleValueType && [
              <Box paddingBlockStart={option.type === "Image swatch" || option.type === "Color swatch" ? "200" : "600"}>
                <Icon
                  key="edit-value-icon"
                  source={DragHandleIcon}
                />
              </Box>,

              // Image upload
              option.type === "Image swatch" && imageUpload,

              // Color picker
              option.type === "Color swatch" && (
                <ColorPicker
                  key="color-picker"
                  color={value.color}
                  onColorChange={(newColor) => onValueChange({ color: newColor })}
                />
              ),

              // Value title
              <div
                key="value-title"
                style={{
                  width: option.type === "Image swatch" || option.type === "Color swatch" ? "7rem" : "11rem",
                  // eslint-disable-next-line no-nested-ternary
                  marginTop: errors.includes("title")
                    ? option.type === "Image swatch" || option.type === "Color swatch"
                      ? "2.58rem"
                      : "1.5rem"
                    : undefined,
                }}
              >
                <TextField
                  label="Value title"
                  value={value.title}
                  placeholder="Red"
                  onChange={(newTitle) => onValueChange({ title: newTitle })}
                  autoComplete="option-value-title"
                  error={errors.includes("title") ? "Title is required" : undefined}
                />
              </div>,
            ]}

            {/* Addon price - not shown for static type */}
            {!isStaticType && (
              <InlineStack
                blockAlign="center"
                gap="200"
                wrap={false}
              >
                <Tooltip
                  content="Upgrade plan to create price add-ons."
                  active={isAddonsAvailable ? false : undefined}
                >
                  <div
                    style={{
                      width: !isSingleValueType ? "6.8rem" : undefined,
                      marginBottom: !isAddonsAvailable ? "-1.5rem" : undefined,
                    }}
                  >
                    <TextField
                      type="number"
                      prefix={currency}
                      min={0}
                      value={value.price !== undefined ? value.price : "0.00"}
                      onChange={(newPrice) => onValueChange({ addonType: "new", price: newPrice })}
                      disabled={!isAddonsAvailable || value.addonType === "existing"}
                      label={
                        <HelpTooltip
                          title="Add-on price"
                          variant="bodyMd"
                          content={
                            isAddonsAvailable
                              ? 'Provide an add-on price to charge an extra price if the value is selected. Alternatively, you can add an existing product as an add-on by clicking "add-on product". In this case, the add-on product will be added to the cart if value is selected.'
                              : "Upgrade plan to create price add-ons."
                          }
                          icon={!isAddonsAvailable ? LockFilledIcon : undefined}
                        />
                      }
                      helpText={
                        !isAddonsAvailable ? (
                          <Button
                            variant="plain"
                            onClick={() => navigate("/pricing")}
                          >
                            Upgrade plan
                          </Button>
                        ) : undefined
                      }
                      onBlur={() =>
                        onValueChange({
                          price: value.price ? parseFloat(value.price).toFixed(2) : "0.00",
                        })
                      }
                      autoComplete="option-value-price"
                    />
                  </div>
                </Tooltip>

                <div style={{ marginTop: "1.5rem" }}>
                  <Box>OR</Box>
                </div>

                {/* Addon picker */}
                <div style={{ marginTop: isAddonsAvailable ? "28px" : "24px" }}>
                  <ProductPicker
                    isDisabled={!isAddonsAvailable}
                    isSingleValueType={isSingleValueType}
                    selectedVariantId={value.addonType === "existing" ? value.addonProduct?.variantId : ""}
                    onVariantAdd={(product) => {
                      if (product) {
                        onValueChange({
                          addonType: "existing",
                          addonProduct: {
                            id: product.productId,
                            variantId: product.id,
                            handle: product.handle,
                          },
                          price: product.price,
                        });
                      }
                    }}
                    onVariantRemove={() => {
                      onValueChange({ addonType: "new", addonProduct: undefined, price: "0.00" });
                    }}
                  />
                </div>
              </InlineStack>
            )}

            {!isSingleValueType &&
              !isStaticType && [
                // Default
                <div
                  key="default"
                  style={{ marginTop: "1.5rem" }}
                >
                  <Checkbox
                    label="Default"
                    checked={value.isDefault}
                    onChange={(newDefault) => {
                      // Don't allow multiple default values for
                      // single-select option types
                      if (
                        !value.isDefault &&
                        newDefault &&
                        isSingleSelectType &&
                        !option.isMultiSelect &&
                        option.values?.some((existingValue) => existingValue.isDefault)
                      ) {
                        setOption((oldOption, valueIndex) => ({
                          ...oldOption,
                          values: oldOption.values?.map((oldValue, index) => ({
                            ...oldValue,
                            isDefault: index === valueIndex,
                          })),
                        }));
                      } else {
                        onValueChange({ isDefault: newDefault });
                      }
                    }}
                  />
                </div>,

                // Duplicate and remove
                <div
                  key="actions"
                  style={{ marginTop: "1.5rem" }}
                >
                  <InlineStack
                    gap="200"
                    wrap={false}
                  >
                    <Button
                      icon={DuplicateIcon}
                      onClick={() =>
                        setOption((oldOption, valueIndex) => {
                          const newValues = [...(oldOption.values || [])];

                          const newValue = {
                            ...(oldOption.values?.[valueIndex] || {}),
                            elementId: generateOptionValueElementId(),
                          };

                          if (newValue.addonType === "new") {
                            delete newValue.addonProduct;
                          }

                          newValues.splice(valueIndex, 0, newValue);

                          return { ...oldOption, values: newValues };
                        })
                      }
                    />

                    <Button
                      disabled={(option.values?.length || 0) <= 1}
                      icon={DeleteIcon}
                      onClick={() => onValueChange(undefined)}
                    />
                  </InlineStack>
                </div>,
              ]}
          </InlineStack>
        )}

        {/* Additional settings */}

        {!isSingleValueType && option.type !== "Dropdown" && !isStaticType && (
          <Box paddingBlockStart="400">
            <Button onClick={() => setShowMoreOptions((isShow) => !isShow)}>Additional settings</Button>
          </Box>
        )}

        {isShowMoreOptions && !isSingleValueType && !isStaticType && (
          <InlineStack
            blockAlign="center"
            gap="400"
          >
            {/* Value description */}
            {option.type !== "Dropdown" && (
              <TextField
                label="Value description"
                value={value.description}
                onChange={(newDescription) => onValueChange({ description: newDescription })}
                autoComplete="option-value-description"
              />
            )}

            {/* Checkbox image */}
            {option.type === "Checkbox" && (
              <Box paddingBlockStart="400">
                <InlineStack>
                  {imageUpload}

                  {option.type === "Checkbox" && (
                    <Box paddingInlineStart="200">
                      <Label id={`image-${value.elementId}`}>Image</Label>
                    </Box>
                  )}
                </InlineStack>
              </Box>
            )}

            {/* Enlarge image on hover */}
            {option.type === "Image swatch" && (
              <Box paddingBlockStart="600">
                <Checkbox
                  label="Enlarge image on hover"
                  checked={value.isEnlargeOnHover}
                  onChange={(newEnlargeOnHover) => onValueChange({ isEnlargeOnHover: newEnlargeOnHover })}
                />
              </Box>
            )}
          </InlineStack>
        )}
      </BlockStack>
    </div>
  );
}

EditValue.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  option: object.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  value: object.isRequired,
  onValueChange: func.isRequired,
  setOption: func.isRequired,
  isSingleValueType: bool.isRequired,
  isSingleSelectType: bool.isRequired,
  isAddonsAvailable: bool.isRequired,
  currency: string,
  errors: arrayOf(string),
  isStaticType: bool.isRequired,
};
