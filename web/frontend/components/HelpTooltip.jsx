import { elementType, string } from 'prop-types';

import { Icon, InlineStack, Text, Tooltip } from '@shopify/polaris';
import { QuestionCircleIcon } from '@shopify/polaris-icons';

/**
 * @param {{
 *  title: string,
 *  content: string,
 *  variant?: import('@shopify/polaris').TextProps['variant'],
 *  icon?: import('@shopify/polaris').IconSource,
 * }} props
 */
export default function HelpTooltip({ title, content, variant = 'headingMd', icon }) {
  return (
    <InlineStack gap="100">
      <Text variant={variant} as="h2">
        {title}
      </Text>

      <Tooltip content={content}>
        <Icon source={icon || QuestionCircleIcon} />
      </Tooltip>
    </InlineStack>
  );
}

HelpTooltip.propTypes = {
  title: string.isRequired,
  content: string.isRequired,
  variant: string,
  icon: elementType,
};
