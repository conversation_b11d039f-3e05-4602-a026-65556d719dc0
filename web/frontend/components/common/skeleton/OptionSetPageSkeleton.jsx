import { BlockStack, Box, Card, EmptyState, InlineGrid, InlineStack, Layout, Page, Text } from "@shopify/polaris";
import React from "react";
import SkeletonLoader from "./Skeleton";

const OptionSetPageSkeleton = () => {
  return (
    <div className="option-set-page-wrapper">
      <Page
        title="Untitled option set"
        backAction={{ content: "Back", onAction: () => {} }}
        primaryAction={
          <Box>
            <SkeletonLoader style={{ height: 28, width: 70 }} />
          </Box>
        }
        secondaryActions={
          <Box>
            <SkeletonLoader style={{ height: 28, width: 70 }} />
          </Box>
        }
      >
        <Layout>
          <Layout.Section>
            <BlockStack gap="400">
              <Card>
                <InlineGrid
                  columns="auto 0.35fr"
                  gap="200"
                >
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 10, width: 70 }} />
                    <SkeletonLoader style={{ height: 25, width: 250 }} />
                    <SkeletonLoader style={{ height: 10, width: 70 }} />
                  </BlockStack>
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 10, width: 50 }} />
                    <SkeletonLoader style={{ height: 25, width: 140 }} />
                  </BlockStack>
                </InlineGrid>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <InlineStack
                    blockAlign="center"
                    gap="400"
                  >
                    <SkeletonLoader style={{ height: 10, width: 100 }} />

                    <SkeletonLoader style={{ height: 10, width: 150 }} />
                  </InlineStack>

                  <Box paddingBlock="200">
                    <SkeletonLoader style={{ height: 10, width: 250 }} />
                  </Box>

                  <BlockStack gap="400">
                    <InlineStack
                      gap="400"
                      align="space-between"
                      blockAlign="center"
                    >
                      <InlineStack
                        gap="400"
                        align="space-between"
                        blockAlign="center"
                      >
                        <SkeletonLoader style={{ height: 20, width: 20 }} />

                        <SkeletonLoader style={{ height: 30, width: 250 }} />
                      </InlineStack>
                      <InlineStack
                        gap="200"
                        align="space-between"
                        blockAlign="center"
                      >
                        <SkeletonLoader style={{ height: 20, width: 20 }} />
                        <SkeletonLoader style={{ height: 20, width: 20 }} />
                      </InlineStack>
                    </InlineStack>
                    <InlineStack
                      gap="200"
                      align="end"
                    >
                      <SkeletonLoader style={{ height: 28, width: 150 }} />
                      <SkeletonLoader style={{ height: 28, width: 150 }} />
                    </InlineStack>
                  </BlockStack>

                  {/* {optionSet.optionIds?.length ? (
                    <Box paddingBlock="200">
                      <InlineStack align="end">
                        <ButtonGroup>
                          {options.length && (
                            <Button onClick={() => setOptionSelectModalOpen(true)}>Add existing option</Button>
                          )}

                          <CreateOptionButton
                            label="Create new option"
                            onCreate={(type) => {
                              setEditedOption({ type });
                              resetAppErrorByKey("optionSet");
                            }}
                            primary
                            isFeatureLimited={shopDetails.isFeatureLimited}
                          />
                        </ButtonGroup>
                      </InlineStack>
                    </Box>
                  ) : undefined} */}
                </BlockStack>
              </Card>

              <Card>
                <Box paddingBlock="200">
                  <BlockStack
                    gap="200"
                    align="center"
                    inlineAlign="center"
                  >
                    <Text
                      as="h2"
                      variant="bodyMd"
                      fontWeight="semibold"
                    >
                      Create rule
                    </Text>
                    <SkeletonLoader style={{ height: 10, width: 350 }} />
                    <SkeletonLoader style={{ height: 10, width: 380 }} />
                    <SkeletonLoader style={{ height: 10, width: 380 }} />
                    <SkeletonLoader style={{ height: 10, width: 300 }} />
                  </BlockStack>
                  <div style={{ marginTop: "1rem" }}>
                    <InlineStack
                      align="center"
                      blockAlign="center"
                      gap="200"
                    >
                      <SkeletonLoader style={{ height: 28, width: 150 }} />
                    </InlineStack>
                  </div>
                </Box>
              </Card>

              <Card background="bg-fill-secondary">
                <Box
                  minHeight="180px"
                  minWidth="200px"
                >
                  &nbsp;
                </Box>
              </Card>
            </BlockStack>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <Card>
              <Box
                minHeight="180px"
                minWidth="200px"
              >
                &nbsp;
              </Box>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </div>
  );
};

export default OptionSetPageSkeleton;
