import { Crisp } from "crisp-sdk-web";
// @ts-ignore
import { func, object } from "prop-types";
import { useEffect, useMemo, useState } from "react";

import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import {
  Banner,
  BlockStack,
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  FormLayout,
  Icon,
  Layout,
  List,
  Page,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import {
  ButtonIcon,
  CalendarIcon,
  CaretDownIcon,
  CheckboxIcon,
  CodeIcon,
  ColorIcon,
  HashtagDecimalIcon,
  ImagesIcon,
  InfoIcon,
  TextBlockIcon,
  TextIcon,
  ToggleOffIcon,
  UploadIcon,
} from "@shopify/polaris-icons";

// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
// eslint-disable-next-line import/no-extraneous-dependencies
import { useNavigate } from "react-router-dom";
// eslint-disable-next-line import/no-relative-packages
import { defaultOptionType } from "../../../defaults";
import CardFooterInfo from "../CardFooterInfo";
import SaveBarWrapper, { saveBarId } from "../common/SaveBarWrapper";
import ScheduleCall from "../common/ScheduleCall";
import CustomCombobox from "../CustomCombobox";
import EditValue, { generateOptionValueElementId } from "../EditValue";
import HelpTooltip from "../HelpTooltip";
import ProductOptionPreviewCard from "../options-templates/preview";
import ProductList from "../ProductList";
import PromptModal from "../PromptModal";
import Tutorials from "../Tutorials";

/** @type {ProductOptionType[]} */
export const productOptionTypes = [
  "Checkbox",
  "Dropdown",
  "Image swatch",
  "Color swatch",
  "Radio button",
  "Button",
  "Text box",
  "Multi-line text box",
  "Number field",
  "Date picker",
  "File upload",
  "HTML",
];

/** @type {Record<ProductOptionType, import('@shopify/polaris').IconSource>} */
export const productOptionIcons = {
  Checkbox: CheckboxIcon,
  Dropdown: CaretDownIcon,
  "Image swatch": ImagesIcon,
  "Radio button": ToggleOffIcon,
  Button: ButtonIcon,
  "Text box": TextIcon,
  "Multi-line text box": TextBlockIcon,
  "Number field": HashtagDecimalIcon,
  "Date picker": CalendarIcon,
  "Color swatch": ColorIcon,
  "File upload": UploadIcon,
  HTML: CodeIcon,
};

/** @type {ProductOptionType[]} */
const singleValueTypes = ["Text box", "Multi-line text box", "Number field", "Date picker", "File upload"];

/** @type {ProductOptionType[]} */
const singleSelectTypes = ["Dropdown", "Button", "Image swatch", "Radio button", "Color swatch"];

/** @type {ProductOptionType[]} */
const multiSelectTypes = ["Image swatch", "Checkbox", "Color swatch"];

/** @type {ProductOptionType[]} */
const staticTypes = ["HTML"];

const mimeTypes = {
  "image/jpeg": "jpg/jpeg",
  "image/png": "png",
  "image/gif": "gif",
  "image/webp": "webp",
  "application/pdf": "pdf",
  "application/msword": "doc",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
  "text/plain": "txt",
  "application/vnd.ms-excel": "xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "text/csv": "csv",
  "audio/mpeg": "mp3",
  "audio/wav": "wav",
  "video/mp4": "mp4",
  "video/quicktime": "mov",
};

/**
 * @param {ProductOptionInput} optionInput
 *
 * @returns {{ option: IProductOption } | { errors: string[] }}
 */
function optionFromInput(optionInput) {
  const errors = [...(!optionInput.title ? ["title"] : []), ...(!optionInput.type ? ["type"] : [])];

  // If option type can only be or can't be multiselect, then set it as multiselect automatically
  const isMultiSelect =
    multiSelectTypes.includes(optionInput.type) &&
    (optionInput.isMultiSelect || !singleSelectTypes.includes(optionInput.type));

  return !errors.length
    ? {
        option: {
          ...optionInput,
          id: optionInput.id || 0,
          title: optionInput.title || "",
          type: optionInput.type,
          description: optionInput.description ? optionInput.description.replace(/[\r\n]+/g, " ") : undefined,
          values:
            optionInput.values?.flatMap((value) => {
              const { imageFile, elementId, ...newValue } = value;
              const price = newValue.price ? parseFloat(newValue.price) : undefined;

              // For HTML type, we want to keep the value even if it doesn't have a title
              // as long as it has htmlContent
              if (optionInput.type === "HTML") {
                return { ...newValue, htmlContent: newValue.htmlContent || "", price };
              }

              return newValue.title ||
                singleValueTypes.includes(optionInput.type) ||
                optionInput.type === "Image swatch" ||
                optionInput.type === "Color swatch"
                ? { ...newValue, title: newValue.title, price }
                : [];
            }) || [],
          productIds: optionInput.productIds || [],
          isMultiSelect,
          minimumSelectionCount: isMultiSelect ? optionInput.minimumSelectionCount || undefined : undefined,
          maximumSelectionCount: isMultiSelect ? optionInput.maximumSelectionCount || undefined : undefined,
          minimumValue:
            optionInput.minimumValue || optionInput.type === "Number field" ? optionInput.minimumValue : undefined,
          maximumValue:
            optionInput.maximumValue || optionInput.type === "Number field" ? optionInput.maximumValue : undefined,
        },
      }
    : { errors };
}

/**
 * @param {ProductOptionInput} optionInput
 * @returns {Record<string, string[]>}
 */
const validateOptionValues = (optionInput) => {
  /** @type {Record<string, string[]>} */
  const errors = {};

  // Skip validation for HTML type
  if (optionInput.type === "HTML") {
    return errors;
  }

  optionInput.values?.forEach((value) => {
    const valueErrors = [];

    if (!value.title) {
      valueErrors.push("title");
    } else {
      const title = value.title.trim();
      if (title.length === 0) {
        valueErrors.push("title");
      }
    }
    // Add more validation checks here if needed
    // if (!value.price) {
    //   valueErrors.push("price");
    // }
    if (valueErrors.length > 0 && value.elementId) {
      errors[value.elementId] = valueErrors;
    }
  });
  return errors;
};

/**
 * @param {Partial<IProductOption> | ProductOptionInput} option
 *
 * @returns {ProductOptionInput}
 */
function optionToInput(option) {
  return {
    ...option,
    type: option.type || defaultOptionType,
    values: option.values?.length
      ? option.values.map((value) => {
          // For HTML type, make sure we preserve the htmlContent
          if (option.type === "HTML") {
            return {
              ...value,
              htmlContent: value.htmlContent || "",
              elementId: generateOptionValueElementId(),
            };
          }

          return {
            ...value,
            price: (Number(value.price) || 0)?.toFixed(2),
            elementId: generateOptionValueElementId(),
          };
        })
      : [{ elementId: generateOptionValueElementId() }],
    productIds: option.productIds?.map((id) => id.toString()),
  };
}

/**
 * @param {{
 *   option: Partial<IProductOption>,
 *   onClose: () => void,
 *   onEdit: (option: IProductOption) => void,
 *   onDelete: (optionId: number | undefined) => void,
 *   shopDetails: IShopDetails,
 * }} props
 */
export default function EditOptionPage({ option: inputOption, onClose, onEdit, onDelete, shopDetails }) {
  const shopify = useAppBridge();
  const navigate = useNavigate();

  const initialOption = useMemo(() => optionToInput(inputOption), [inputOption]);

  const [option, setOption] = useState(/** @type {ProductOptionInput} */ (initialOption));
  const [savedOption, setSavedOption] = useState(option);

  const [isSaveLoading, setSaveLoading] = useState(false);
  const [isDeleteLoading, setDeleteLoading] = useState(false);
  const [errors, setErrors] = useState(/** @type {string[]} */ ([]));
  const [optionValuesErrors, setOptionValuesErrors] = useState(/** @type {Record<number, string[]>} */ ({}));

  /** @type {TypedStateOptional<'delete' | 'discard'>} */
  const [openModalType, setOpenModalType] = useState();

  useEffect(() => {
    const defaultValues = option.values?.filter((value) => value.isDefault) || [];

    // Don't allow multiple default values for single-select option types
    if (singleSelectTypes.includes(option.type) && !option.isMultiSelect && defaultValues.length > 1) {
      setOption((oldOption) => ({
        ...oldOption,
        values: oldOption.values?.map((value) => (value === defaultValues[0] ? value : { ...value, isDefault: false })),
      }));
    }

    // If it's a single-valued option, keep only the first value without the title object
    if (
      singleValueTypes.includes(option.type) &&
      option.values &&
      (option.values[0].title || option.values.length > 1)
    ) {
      setOption((oldOption) => {
        const newValue = oldOption.values?.[0];
        delete newValue?.title;

        return { ...oldOption, values: [...(newValue ? [newValue] : [])] };
      });
    }

    // If it's a static HTML option, keep only the first value
    if (staticTypes.includes(option.type) && option.values && option.values.length > 1) {
      setOption((oldOption) => {
        const newValue = oldOption.values?.[0];
        return { ...oldOption, values: [...(newValue ? [newValue] : [])] };
      });
    }

    // If it's a file upload option, set allowed file types to default
    if (option.type === "File upload") {
      setOption((oldOption) =>
        !oldOption.allowedFileTypes ? { ...oldOption, allowedFileTypes: ["image/jpeg", "image/png"] } : oldOption
      );
    }
  }, [option.type, option.isMultiSelect, option.values]);

  /**
   * @template {keyof ProductOptionInput} TName
   *
   * @param {TName} optionName
   * @returns {(value: ProductOptionInput[TName]) => void}
   */
  const createOptionSetter = (optionName) => (value) => {
    setOption((oldOption) => ({ ...oldOption, [optionName]: value }));
  };

  /**
   * @param {number} index
   * @param {Exclude<ProductOptionInput['values'], undefined>[number] | undefined} value
   */
  const setOptionValue = (index, value) => {
    setOption((oldOption) => {
      const newValues = [...(oldOption.values || [])];

      if (value) {
        newValues[index] = { ...newValues[index], ...value };
      } else {
        newValues.splice(index, 1);
      }

      return { ...oldOption, values: newValues };
    });
  };

  const saveOption = async () => {
    setErrors([]);
    setOptionValuesErrors({});
    setSaveLoading(true);

    const newImageValues = option.values?.filter((value) => value.imageFile);

    if (newImageValues?.length) {
      const imageData = newImageValues.map((value) => ({
        filename: value.imageFile?.name,
        mimeType: value.imageFile?.type,
      }));

      // Generate upload URLs for the images
      const imageURLResponse = await fetch("/api/imageUpload/createUrls", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ images: imageData }),
      });

      /** @type {ImageUploadCreateUrlsResponse & { error?: string }} */
      const imageURLData = await imageURLResponse.json();
      const imageUploadRequests = imageURLResponse.ok ? imageURLData.data : undefined;

      if (imageUploadRequests?.length !== imageData.length) {
        const errorMessage = imageURLData.error;
        setErrors((oldErrors) => [...oldErrors, `image_upload::${errorMessage}`]);
        setSaveLoading(false);
        shopify.saveBar.hide(saveBarId);
        return;
      }

      // Upload all images to the generated URLs
      await Promise.all(
        imageUploadRequests.map(({ url, parameters }, index) =>
          fetch(url, {
            method: "PUT",
            headers: Object.fromEntries(parameters.map(({ name, value }) => [name, value])),
            body: newImageValues[index].imageFile,
          })
        )
      );

      const fileData = imageUploadRequests?.map(({ resourceUrl }, index) => ({
        resourceUrl,
        filename: newImageValues[index].imageFile?.name,
        altText: newImageValues[index].title,
      }));

      // Create files from the uploaded images
      const fileCreateResponse = await fetch("/api/imageUpload/createFiles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ images: fileData }),
      });

      /** @type {ImageUploadCreateFilesResponse['data']} */
      const fileCreateResult = fileCreateResponse.ok && (await fileCreateResponse.json()).data;
      if (fileCreateResult?.length !== fileData.length) {
        const errorMessage = (await fileCreateResponse.json()).error;
        setErrors((oldErrors) => [...oldErrors, `image_upload::${errorMessage}`]);
        setSaveLoading(false);
        return;
      }

      newImageValues.forEach((value, index) => {
        // eslint-disable-next-line no-param-reassign
        value.image = fileCreateResult[index];
        // eslint-disable-next-line no-param-reassign
        delete value.imageFile;
      });
    }

    if (["Checkbox", "Dropdown", "Image swatch", "Color swatch", "Radio button", "Button"].includes(option.type)) {
      const optionInputValuesErrors = validateOptionValues(option);

      if (Object.keys(optionInputValuesErrors).length > 0) {
        setOptionValuesErrors(optionInputValuesErrors);
        setSaveLoading(false);
        return;
      }
    }

    // For HTML type, we don't need to validate values
    if (staticTypes.includes(option.type)) {
      // Make sure we have at least one value for HTML content
      if (!option.values || option.values.length === 0) {
        setOption((oldOption) => ({
          ...oldOption,
          values: [{ elementId: generateOptionValueElementId(), htmlContent: "" }],
        }));
      } else if (option.values.length > 0) {
        // Make sure the first value has htmlContent property
        setOption((oldOption) => {
          const newValues = [...(oldOption.values || [])];
          if (!newValues[0].htmlContent) {
            newValues[0] = { ...newValues[0], htmlContent: "" };
          }
          return { ...oldOption, values: newValues };
        });
      }
    }

    const changedOption = optionFromInput(option);

    if ("errors" in changedOption) {
      setErrors((oldErrors) => [...oldErrors, ...changedOption.errors]);
      setSaveLoading(false);
      return;
    }

    const response = await fetch("/api/options/save", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ options: [changedOption.option] }),
    });

    if (response.ok) {
      /** @type {OptionItems} */
      const updatedOptions = await response.json();
      const updatedOption = updatedOptions.options?.[0];

      if (updatedOption) {
        const updatedOptionInput = optionToInput(updatedOption);

        setOption(updatedOptionInput);
        setSavedOption(updatedOptionInput);

        onEdit(updatedOption);
        shopify.toast.show("Option saved");
      }
    }
    setSaveLoading(false);
    shopify.saveBar.hide(saveBarId);
  };

  const deleteOption = async () => {
    setDeleteLoading(true);

    const response = await fetch("/api/options/delete", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ optionIds: [option.id] }),
    });

    if (response.ok) {
      Crisp.session.pushEvent("delete_option");
      onDelete(option.id);

      return;
    }

    setDeleteLoading(false);
  };

  const isAddonsAvailable = !shopDetails.isFeatureLimited || shopDetails.isAddonsFree;
  const isSingleValueType = singleValueTypes.includes(option.type);

  let optionNamePlaceholder = singleValueTypes.includes(option.type) ? "Custom text" : "Color";

  optionNamePlaceholder = option.type === "Date picker" ? "Delivery date" : optionNamePlaceholder;

  useEffect(() => {
    const handler = setTimeout(() => {
      // Existing 0 ID means that this is a duplicated option
      if (option !== savedOption || option.id === 0) {
        shopify.saveBar.show(saveBarId);
      } else {
        shopify.saveBar.hide(saveBarId);
      }
    }, 200);
    return () => clearTimeout(handler);
  }, [option, savedOption, shopify]);

  return (
    <div className="option-page-wrapper">
      <Page
        title={option.nickname || option.title || "Untitled option"}
        backAction={{
          content: "Options",
          onAction: () => {
            if (option !== savedOption) {
              shopify.saveBar.leaveConfirmation();
              // setOpenModalType("discard");
            } else {
              onClose();
            }
          },
        }}
        primaryAction={
          option.id
            ? {
                content: "Delete option",
                destructive: true,
                onAction: () => setOpenModalType("delete"),
              }
            : undefined
        }
      >
        <SaveBarWrapper
          saveActionCallback={() => {
            saveOption();
          }}
          discardActionCallback={() => {
            // eslint-disable-next-line no-unused-expressions
            if (option !== savedOption) {
              setErrors([]);
              setOption(savedOption);
              shopify.saveBar.hide(saveBarId);
            } else {
              onClose();
            }
          }}
          loading={isSaveLoading}
        />
        {errors.find((error) => error.startsWith("image_upload")) && (
          <Box paddingBlockEnd="400">
            <Banner
              tone="critical"
              title="Error"
              onDismiss={() => setErrors((oldErrors) => oldErrors.filter((error) => !error.startsWith("image_upload")))}
            >
              <p>
                Failed to upload image:{" "}
                {errors.find((error) => error.startsWith("image_upload"))?.replace("image_upload::", "") ||
                  "Please try again."}
              </p>
            </Banner>
          </Box>
        )}

        <Layout>
          <Layout.Section>
            <BlockStack gap="400">
              <Card>
                <FormLayout>
                  <FormLayout.Group>
                    <TextField
                      label={
                        <HelpTooltip
                          title="Option name"
                          content='The option name is visible for customers and describes the thing they are customizing. For example, if you sell T-shirts, the option name could be "Size" or "Color".'
                        />
                      }
                      placeholder={optionNamePlaceholder}
                      value={option.title}
                      error={errors.includes("title") ? "Option title is required" : undefined}
                      onChange={createOptionSetter("title")}
                      autoComplete="option-title"
                      helpText="Visible to customers"
                    />

                    <Select
                      label="Option type"
                      value={option.type}
                      onChange={(newType) => {
                        if (shopDetails.isFeatureLimited && newType === "File upload") {
                          navigate("/pricing");
                          return;
                        }

                        createOptionSetter("type")(/** @type {ProductOptionType} */ (newType));
                      }}
                      options={productOptionTypes.map((type) => ({
                        label: shopDetails.isFeatureLimited && type === "File upload" ? `${type} (Upgrade plan)` : type,
                        value: type,
                      }))}
                    />
                  </FormLayout.Group>
                </FormLayout>
              </Card>
              <Card>
                <Text
                  as="h2"
                  variant="headingSm"
                >
                  {(() => {
                    if (isSingleValueType) {
                      return "Price";
                    }
                    if (staticTypes.includes(option.type)) {
                      return "HTML content";
                    }
                    return (
                      <HelpTooltip
                        title="Option values"
                        content='Option values are the specific customizations that your customers can choose from. If you sell T-shirts and your option is the "color" option, you can add the exact colors to choose from as values (eg. white).'
                      />
                    );
                  })()}
                </Text>

                <Box paddingBlock="200">
                  <DragDropContext
                    onDragEnd={({ source, destination }) =>
                      destination &&
                      setOption((oldOption) => {
                        const newValues = [...(oldOption.values || [])];
                        const [removed] = newValues.splice(source.index, 1);
                        newValues.splice(destination.index, 0, removed);

                        return { ...oldOption, values: newValues };
                      })
                    }
                  >
                    <Droppable droppableId="droppable">
                      {(provided) => (
                        // eslint-disable-next-line react/jsx-props-no-spreading
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                        >
                          {option.values?.map(
                            (value, valueIndex) =>
                              value.elementId && (
                                <Draggable
                                  key={value.elementId}
                                  draggableId={value.elementId}
                                  index={valueIndex}
                                  isDragDisabled={isSingleValueType || staticTypes.includes(option.type)}
                                >
                                  {(draggableProvided) => (
                                    <div
                                      ref={draggableProvided.innerRef}
                                      // eslint-disable-next-line react/jsx-props-no-spreading
                                      {...draggableProvided.draggableProps}
                                      // eslint-disable-next-line react/jsx-props-no-spreading
                                      {...draggableProvided.dragHandleProps}
                                    >
                                      <Box paddingBlock="200">
                                        <EditValue
                                          // @ts-ignore
                                          errors={optionValuesErrors[value.elementId]}
                                          option={option}
                                          value={value}
                                          onValueChange={(newValue) => setOptionValue(valueIndex, newValue)}
                                          setOption={(setter) =>
                                            setOption((oldOption) => setter(oldOption, valueIndex))
                                          }
                                          isSingleValueType={isSingleValueType}
                                          isSingleSelectType={singleSelectTypes.includes(option.type)}
                                          isAddonsAvailable={isAddonsAvailable}
                                          currency={shopDetails.currency}
                                          isStaticType={staticTypes.includes(option.type)}
                                        />

                                        {!isSingleValueType && !staticTypes.includes(option.type) && (
                                          <Box paddingBlockStart="400">
                                            <Divider />
                                          </Box>
                                        )}
                                      </Box>
                                    </div>
                                  )}
                                </Draggable>
                              )
                          )}

                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </Box>

                {!isSingleValueType && !staticTypes.includes(option.type) && (
                  <Button
                    onClick={() =>
                      setOption((oldOption) => ({
                        ...oldOption,
                        values: [...(oldOption.values || []), { elementId: generateOptionValueElementId() }],
                      }))
                    }
                  >
                    Add value
                  </Button>
                )}
                {staticTypes.includes(option.type) && (
                  <CardFooterInfo
                    content={
                      <>
                        <Box>
                          <Icon source={InfoIcon} />
                        </Box>
                        <Text
                          as="p"
                          variant="bodySm"
                        >
                          Script tags and JavaScript event handlers will be automatically removed for security reasons
                        </Text>
                      </>
                    }
                  />
                )}
              </Card>
              {option.productIds && !!option.productIds.length && (
                <Card>
                  <Text
                    as="h2"
                    variant="headingSm"
                  >
                    Add option to products
                  </Text>

                  <Box paddingBlock="200">
                    <ProductList
                      selectedIds={option.productIds || []}
                      onSelectedIdsChange={(newSelectedIds) =>
                        setOption((oldOption) => ({ ...oldOption, productIds: newSelectedIds }))
                      }
                    />
                  </Box>
                </Card>
              )}
              <Tutorials
                showIndexes={[2]}
                columns={{ sm: 1, lg: 2 }}
                showScheduleCallCard
                scheduleCallCardContent={
                  <ScheduleCall
                    showActions
                    isPortrait
                    title="Get expert help from us"
                    description={
                      <>
                        Let us help create all product options for{" "}
                        <Text
                          as="strong"
                          variant="bodySm"
                          fontWeight="semibold"
                        >
                          FREE
                        </Text>
                        .
                      </>
                    }
                    pageType="options"
                  />
                }
              />
            </BlockStack>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="400">
              <ProductOptionPreviewCard option={option} />

              {!staticTypes.includes(option.type) && (
                <Card>
                  <Text
                    as="h2"
                    variant="headingSm"
                  >
                    Input settings
                  </Text>
                  <Box paddingBlockStart="200">
                    <BlockStack gap="200">
                      <Checkbox
                        label="Required option"
                        checked={option.isRequired}
                        onChange={createOptionSetter("isRequired")}
                        helpText="Customer must make a selection to checkout"
                      />

                      {option.type === "File upload" && (
                        <CustomCombobox
                          label="Allowed file types"
                          values={[
                            ...(option.allowedFileTypes?.flatMap((type) =>
                              !(type in mimeTypes) ? [[type, type]] : []
                            ) || []),
                            ...Object.entries(mimeTypes),
                          ].map(([mimeType, name]) => ({ id: mimeType, label: name }))}
                          selectedItems={option.allowedFileTypes || []}
                          onSelectionChange={createOptionSetter("allowedFileTypes")}
                        />
                      )}

                      {multiSelectTypes.includes(option.type) && (
                        <BlockStack>
                          {multiSelectTypes.includes(option.type) && singleSelectTypes.includes(option.type) && (
                            <Checkbox
                              label="Allow multiple selection"
                              checked={option.isMultiSelect}
                              onChange={createOptionSetter("isMultiSelect")}
                            />
                          )}

                          {(option.isMultiSelect || option.type === "Checkbox") && (
                            <TextField
                              label="Minimum selectable values"
                              type="number"
                              value={option.minimumSelectionCount?.toString()}
                              onChange={(newValue) =>
                                createOptionSetter("minimumSelectionCount")(parseInt(newValue, 10))
                              }
                              min={0}
                              autoComplete="option-minimum-selection-count"
                            />
                          )}

                          {(option.isMultiSelect || option.type === "Checkbox") && (
                            <TextField
                              label="Maximum selectable values"
                              type="number"
                              value={option.maximumSelectionCount?.toString()}
                              onChange={(newValue) =>
                                createOptionSetter("maximumSelectionCount")(parseInt(newValue, 10))
                              }
                              min={0}
                              autoComplete="option-maximum-selection-count"
                            />
                          )}
                        </BlockStack>
                      )}

                      {!singleValueTypes.includes(option.type) && (
                        <Checkbox
                          label="Show selection next to option title"
                          checked={option.isShowSelectedValues}
                          onChange={createOptionSetter("isShowSelectedValues")}
                        />
                      )}

                      {(option.type === "Text box" ||
                        option.type === "Multi-line text box" ||
                        option.type === "Number field") && (
                        <TextField
                          label="Placeholder text"
                          value={option.placeholderText}
                          onChange={createOptionSetter("placeholderText")}
                          autoComplete="off"
                        />
                      )}
                      {(option.type === "Text box" ||
                        option.type === "Multi-line text box" ||
                        option.type === "Number field") && (
                        <BlockStack>
                          <TextField
                            label={`Minimum ${option.type === "Number field" ? "number" : "character limit"}`}
                            type="number"
                            value={option.minimumValue?.toString()}
                            onChange={(newValue) => createOptionSetter("minimumValue")(parseInt(newValue, 10))}
                            min={option.type !== "Number field" ? 0 : undefined}
                            autoComplete="option-minimum-character-limit"
                          />

                          <TextField
                            label={`Maximum ${option.type === "Number field" ? "number" : "character limit"}`}
                            type="number"
                            value={option.maximumValue?.toString()}
                            onChange={(newValue) => createOptionSetter("maximumValue")(parseInt(newValue, 10))}
                            min={option.type !== "Number field" ? 0 : undefined}
                            autoComplete="option-maximum-character-limit"
                          />
                        </BlockStack>
                      )}
                    </BlockStack>
                  </Box>
                </Card>
              )}
              <Card>
                <Text
                  as="h2"
                  variant="headingSm"
                >
                  Additional settings
                </Text>
                <Box paddingBlockStart="200">
                  <BlockStack gap="200">
                    {!staticTypes.includes(option.type) && (
                      <>
                        <TextField
                          label="In-cart name"
                          value={option.inCartName}
                          onChange={createOptionSetter("inCartName")}
                          autoComplete="option-in-cart-name"
                          helpText="Name shown in the cart, by default it is the option title"
                        />
                        <TextField
                          label="Option nickname"
                          value={option.nickname}
                          onChange={createOptionSetter("nickname")}
                          autoComplete="option-nickname"
                          helpText="Only visible to you"
                        />
                      </>
                    )}
                    <TextField
                      label="Option description"
                      multiline={4}
                      value={option.description}
                      onChange={createOptionSetter("description")}
                      autoComplete="off"
                      helpText={
                        <List gap="extraTight">
                          <List.Item>Visible to customers</List.Item>
                          <List.Item>Newlines will be treated as spaces</List.Item>
                        </List>
                      }
                    />
                  </BlockStack>
                </Box>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>

        <div style={{ height: "1rem" }} />

        <PromptModal
          title={openModalType === "delete" ? "Delete option" : "Discard changes"}
          prompt={
            openModalType === "delete"
              ? "Are you sure you want to delete this option? This cannot be undone."
              : "Are you sure you want to discard the changes?"
          }
          actionLabel={openModalType === "delete" ? "Delete" : "Discard"}
          isDestructive
          isOpen={!!openModalType}
          isLoading={isDeleteLoading}
          onClose={async (isConfirmed) => {
            if (isConfirmed) {
              if (openModalType === "delete") {
                await deleteOption();
              } else {
                onClose();
              }
            }

            setOpenModalType(undefined);
          }}
        />
      </Page>
    </div>
  );
}

EditOptionPage.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  option: object.isRequired,
  onClose: func.isRequired,
  onEdit: func.isRequired,
  onDelete: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  shopDetails: object.isRequired,
};
