import { Crisp } from "crisp-sdk-web";
import { arrayOf, bool, func, object } from "prop-types";
import { lazy, Suspense, useEffect, useMemo, useState } from "react";

import {
  Banner,
  Bleed,
  BlockStack,
  Box,
  Button,
  Card,
  EmptyState,
  InlineStack,
  Page,
  Text,
  Tooltip,
} from "@shopify/polaris";

// eslint-disable-next-line import/no-relative-packages, import/no-extraneous-dependencies
import { useNavigate } from "react-router-dom";
// eslint-disable-next-line import/no-relative-packages
import { defaultFreePlanMaxOptionSets as freePlanMaxOptionSets } from "../../../defaults";
import configs from "../../configs";
import { useAppQuery, useTrackEvent } from "../../utilities/hooks";
import ScheduleCall from "../common/ScheduleCall";
import HomePageSkeleton from "../common/skeleton/HomePageSkeleton";
import TutorialsSkeleton from "../common/skeleton/TutorialsSkeleton";
import CreateOptionButton from "../CreateOptionButton";
import Partners from "../Partners";
import ProductOptionList from "../ProductOptionList";
import PromptModal from "../PromptModal";
import { useStore } from "../providers/StoreProvider";

const Tutorials = lazy(() => import("../Tutorials"));

/**
 * @param {{
 *   isInitialized: boolean,
 *   isOptionsLoading: boolean,
 *   optionSets: IProductOptionSet[],
 *   setOptionSets: React.Dispatch<React.SetStateAction<IProductOptionSet[]>>,
 *   options: IProductOption[],
 *   setOptions: React.Dispatch<React.SetStateAction<IProductOption[]>>,
 *   onOptionSetEdit: (optionSet: Partial<IProductOptionSet>) => void,
 *   onOptionEdit: (option: Partial<IProductOption>) => void,
 *   isLimitReached: boolean,
 *   isEnableExtensionDismissed: boolean,
 *   onEnableExtensionDismiss: () => void,
 * }} props
 */
export default function HomePage({
  isInitialized,
  isOptionsLoading,
  optionSets,
  setOptionSets,
  options,
  setOptions,
  onOptionSetEdit,
  onOptionEdit,
  isLimitReached,
  isEnableExtensionDismissed,
  onEnableExtensionDismiss,
}) {
  const { shopDetails } = useStore();
  const navigate = useNavigate();

  /** @type {TypedStateOptional<OptionItemIds>} */
  const [deletedOptionsAndSets, setDeletedOptionsAndSets] = useState();
  const [isDeleteLoading, setDeleteLoading] = useState(false);

  const [isDuplicateLoading, setIsDuplicateLoading] = useState(false);

  const initialAppPartnersDismissed = useMemo(() => localStorage.getItem("isAppPartnersDismissed") === "true", []);
  const initialBookCallBannerDismissed = useMemo(
    () => localStorage.getItem("isBookCallBannerDismissed") === "true",
    []
  );
  const isLimitReachedBannerDismissed = useMemo(
    () => localStorage.getItem("isLimitReachedBannerDismissed") === "true",
    []
  );
  const isRemoveWatermarkBannerDismissed = useMemo(
    () => localStorage.getItem("isRemoveWatermarkBannerDismissed") === "true",
    []
  );

  const [isBookCallBannerDismissed, setBookCallBannerDismissed] = useState(initialBookCallBannerDismissed);
  const [isAppPartnersDismissed, setAppPartnersDismissed] = useState(initialAppPartnersDismissed);
  const [isLimitDismissed, setIsLimitDismissed] = useState(isLimitReachedBannerDismissed);
  const [isRemoveWatermarkDismissed, setIsRemoveWatermarkDismissed] = useState(isRemoveWatermarkBannerDismissed);

  const trackEvent = useTrackEvent();

  /** @type {UseQueryResult<boolean>} */
  const { data: isAppBlockAdded, isLoading: isAppBlockEnabledLoading } = useAppQuery("api/appBlock/enabled");

  useEffect(() => {
    Crisp.session.pushEvent("main_page");
  }, []);

  const duplicateSelectedOptionsAndSets =
    /** @type {(optionSetIds: number[], optionIds: number[]) => Promise<void>} */
    async (optionSetIds, optionIds) => {
      setIsDuplicateLoading(true);

      let newOptionSetIds = optionSetIds;

      // If the user is on a limited plan, only allow them to duplicate up to the max number of
      // option sets
      if (shopDetails?.isFeatureLimited && optionSetIds.length + optionSets.length > freePlanMaxOptionSets) {
        newOptionSetIds = optionSetIds.slice(0, freePlanMaxOptionSets - optionSets.length);
      }

      const data = {
        options: options
          .filter((option) => optionIds.includes(option.id))
          .map((option) => ({
            ...option,
            id: 0,
            nickname: `Copy of ${option.nickname || option.title}`,
            values: option.values.map((value) => {
              const { addonProduct, ...newValue } = value;
              return value.addonType === "new" ? newValue : value;
            }),
          })),
        optionSets: optionSets
          .filter((optionSet) => newOptionSetIds.includes(optionSet.id))
          .map((optionSet) => ({
            ...optionSet,
            title: `Copy of ${optionSet.title}`,
            id: 0,
            productIds: [],
            productSelectionValues: [],
          })),
      };

      const response = await fetch("/api/options/save", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        /** @type {OptionItems} */
        const { optionSets: newOptionSets = [], options: newOptions = [] } = await response.json();

        setOptionSets((oldOptionSets) => [...oldOptionSets, ...newOptionSets]);
        setOptions((oldOptions) => [...oldOptions, ...newOptions]);
      }

      setIsDuplicateLoading(false);
    };

  const deleteSelectedOptionsAndSets = async () => {
    setDeleteLoading(true);

    const response = await fetch("/api/options/delete", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(deletedOptionsAndSets),
    });

    if (response.ok) {
      if (deletedOptionsAndSets?.optionSetIds?.length) {
        Crisp.session.pushEvent("delete_option_set");
      }

      if (deletedOptionsAndSets?.optionIds?.length) {
        Crisp.session.pushEvent("delete_option");
      }

      setOptionSets((oldOptionSets) => [
        ...oldOptionSets.flatMap((optionSet) =>
          optionSet.id && !deletedOptionsAndSets?.optionSetIds?.includes(optionSet.id)
            ? {
                ...optionSet,
                optionIds: optionSet.optionIds.filter((id) => !deletedOptionsAndSets?.optionIds?.includes(id)),
              }
            : []
        ),
      ]);

      setOptions((oldOptions) => oldOptions.filter((option) => !deletedOptionsAndSets?.optionIds?.includes(option.id)));

      setDeletedOptionsAndSets(undefined);
    }

    setDeleteLoading(false);
  };

  const deletedOptionsAndSetsText = [
    ...(deletedOptionsAndSets?.optionIds?.length ? ["options"] : []),
    ...(deletedOptionsAndSets?.optionSetIds?.length ? ["option sets"] : []),
  ].join(" and ");

  const emptyContent = (
    <Card padding="0">
      <EmptyState
        heading="Create your first option set"
        // `action` property does not accept ReactNode, so creating Popover is not possible
        footerContent={
          <div style={{ marginBottom: "-2rem" }}>
            <InlineStack gap="400">
              <CreateOptionButton
                label="Create option"
                onCreate={(type) => onOptionEdit({ type })}
                isFeatureLimited={!!shopDetails?.isFeatureLimited}
              />

              <Button
                onClick={() => onOptionSetEdit({})}
                variant="primary"
                disabled={isLimitReached}
              >
                Create option set
              </Button>
            </InlineStack>
          </div>
        }
        image=""
      >
        <p>
          Every option set consists of one or more options. One option is a specific thing the customer can customize
          (e.g., Color). There are several option types to choose from (e.g., checkbox, text box).
        </p>
      </EmptyState>
    </Card>
  );

  const isShowExtensionDisabledBanner =
    shopDetails &&
    !isAppBlockEnabledLoading &&
    !isAppBlockAdded &&
    // `isExtensionEnabled` check prevents showing banner for previous users
    !shopDetails.isExtensionEnabled &&
    !isEnableExtensionDismissed;

  const [showPageHeadingBanner, setShowPageHeadingBanner] = useState(false);

  useEffect(() => {
    // @ts-ignore
    let timer;
    if (shopDetails && (!isAppBlockEnabledLoading || !isEnableExtensionDismissed) && !showPageHeadingBanner) {
      timer = setTimeout(() => {
        setShowPageHeadingBanner(true);
      }, 300);
    }
    return () => {
      // @ts-ignore
      if (shopDetails && timer) {
        clearTimeout(timer);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAppBlockEnabledLoading, shopDetails, isAppBlockAdded, isBookCallBannerDismissed, showPageHeadingBanner]);

  return (
    <div className="homepage-container">
      <Page
        title="EasyFlow Product Options"
        secondaryActions={
          <CreateOptionButton
            label="Create option"
            onCreate={(type) => onOptionEdit({ type })}
          />
        }
        primaryAction={
          <Tooltip
            content={isLimitReached && "Upgrade plan to create unlimited option sets"}
            hoverDelay={isLimitReached ? 0 : 100000}
          >
            <Button
              onClick={() => onOptionSetEdit({})}
              variant="primary"
              disabled={isLimitReached}
            >
              Create option set
            </Button>
          </Tooltip>
        }
      >
        <Box>
          {!showPageHeadingBanner && (
            <div className="homepage-static-loader">
              <HomePageSkeleton />
            </div>
          )}
          <div className={`homepage-content ${showPageHeadingBanner ? "visible" : ""}`}>
            <BlockStack gap="400">
              {showPageHeadingBanner && isShowExtensionDisabledBanner && (
                <Banner
                  tone="warning"
                  title="Options are not visible"
                  action={{
                    content: "Activate",
                    onAction: () => window.open(`shopify://admin${configs.appBlockDeepLinkPath}`, "_blank"),
                  }}
                  secondaryAction={{
                    content: "Guide",
                    onAction: () =>
                      window.open(
                        "https://easyflow-options.crisp.help/en/article/show-options-on-storefront-1s1olfe/",
                        "_blank"
                      ),
                  }}
                  onDismiss={() => {
                    onEnableExtensionDismiss();
                    fetch("/api/extension/enabled", { method: "POST" });
                  }}
                >
                  The EasyFlow app block is not activated in your theme. To show the options on the store front add the
                  EasyFlow app block to the product page in the theme editor.
                </Banner>
              )}
              {!isShowExtensionDisabledBanner &&
                shopDetails?.isSubscriptionActive &&
                !isBookCallBannerDismissed &&
                showPageHeadingBanner && (
                  <Banner
                    title="Setup call + 1 month FREE"
                    action={{
                      content: "Book call",
                      onAction: () => window.open("https://storeware.io/easyflow/talk-with-expert", "_blank"),
                    }}
                    onDismiss={() => {
                      setBookCallBannerDismissed(true);
                      localStorage.setItem("isBookCallBannerDismissed", "true");
                    }}
                  >
                    Book a setup call so we can make sure the app is set up correctly on your webshop & we can answer
                    any questions you have. 🤩✅ We&apos;ll also give you 1 FREE month of our Unlimited Plan after the
                    call.🤑🤗
                  </Banner>
                )}
              {!isShowExtensionDisabledBanner && shopDetails?.isFeatureLimited && showPageHeadingBanner && (
                <>
                  {!isLimitDismissed && isLimitReached && (
                    <Banner
                      title="Option set limit reached"
                      tone="warning"
                      onDismiss={() => {
                        setIsLimitDismissed(true);
                        localStorage.setItem("isLimitReachedBannerDismissed", "true");
                      }}
                      secondaryAction={{
                        content: "Upgrade plan",
                        onAction: () => {
                          trackEvent("click upgrade plan", { from: "limit_banner" });
                          navigate("/pricing");
                        },
                      }}
                    >
                      Upgrade your plan to create unlimited option sets and to remove the EasyFlow watermark on the
                      storefront.
                    </Banner>
                  )}
                  {!isRemoveWatermarkDismissed && (
                    <Banner
                      title="Remove watermark"
                      tone="info"
                      onDismiss={() => {
                        setIsRemoveWatermarkDismissed(true);
                        localStorage.setItem("isRemoveWatermarkBannerDismissed", "true");
                      }}
                      secondaryAction={{
                        content: "Remove watermark",
                        onAction: () => {
                          trackEvent("click remove watermark", { from: "watermark_banner" });
                          Crisp.message.send("text", "Remove watermark for free");
                          Crisp.chat.open();
                        },
                      }}
                    >
                      We’re happy to remove the watermark for you and make sure you’re satisfied with EasyFlow.
                    </Banner>
                  )}
                </>
              )}
              {optionSets.length + options.length || !isInitialized ? (
                <ProductOptionList
                  isLimitReached={isLimitReached}
                  optionSets={optionSets}
                  options={options}
                  isLoading={!isInitialized || isDuplicateLoading || isOptionsLoading}
                  isMoreResultsLoading={isOptionsLoading}
                  onOptionEdit={onOptionEdit}
                  onOptionSetEdit={onOptionSetEdit}
                  onDuplicate={(optionSetIds, optionIds) => duplicateSelectedOptionsAndSets(optionSetIds, optionIds)}
                  onDelete={(optionSetIds, optionIds) => setDeletedOptionsAndSets({ optionSetIds, optionIds })}
                />
              ) : (
                emptyContent
              )}
              <Box paddingBlockStart="200">
                <Text
                  variant="headingLg"
                  as="h5"
                >
                  Setup help
                </Text>
              </Box>
              <Suspense fallback={<TutorialsSkeleton />}>
                <Tutorials showIndexes={[0, 1, 3, 4]} />
              </Suspense>
              <Bleed marginBlockStart="400">
                <ScheduleCall
                  title="Get help from our experts to create your options and option sets"
                  description={
                    <>
                      Need assistance? Our experts can quickly set up your store’s options and option sets. Schedule a
                      meeting now to consult an expert for{" "}
                      <Text
                        as="span"
                        variant="bodySm"
                        fontWeight="semibold"
                      >
                        FREE
                      </Text>
                      .
                    </>
                  }
                  pageType="dashboard"
                />
              </Bleed>
              {!isAppPartnersDismissed && <Partners onDismiss={() => setAppPartnersDismissed(true)} />}
            </BlockStack>

            <PromptModal
              title={`Delete selected ${deletedOptionsAndSetsText}`}
              prompt={`Are you sure you want to delete the selected ${deletedOptionsAndSetsText}? This cannot be undone.`}
              actionLabel="Delete"
              isOpen={!!deletedOptionsAndSets}
              isLoading={isDeleteLoading}
              onClose={async (isConfirmed) => {
                if (isConfirmed) {
                  await deleteSelectedOptionsAndSets();
                } else {
                  setDeletedOptionsAndSets(undefined);
                }
              }}
              isDestructive
            />
          </div>
        </Box>
      </Page>
    </div>
  );
}

HomePage.propTypes = {
  isInitialized: bool.isRequired,
  isOptionsLoading: bool.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  optionSets: arrayOf(object).isRequired,
  setOptionSets: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: arrayOf(object).isRequired,
  setOptions: func.isRequired,
  onOptionSetEdit: func.isRequired,
  onOptionEdit: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  isLimitReached: bool.isRequired,
  isEnableExtensionDismissed: bool.isRequired,
  onEnableExtensionDismiss: func.isRequired,
};
