import { NavMenu } from "@shopify/app-bridge-react";
import { mainNavigationMenu } from "../../configs/routes";

const AppNavigationProvider = () => (
  <NavMenu>
    {mainNavigationMenu.map((route) => (
      <a
        key={`${route.label}-${Math.random()}`}
        href={route.destination}
        rel={route?.rel ? "home" : ""}
      >
        {route.label}
      </a>
    ))}
  </NavMenu>
);

export default AppNavigationProvider;
