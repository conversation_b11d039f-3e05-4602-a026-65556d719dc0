import { useCallback, useEffect, useState } from "react";
import useThirdPartyScripts from "../../hooks/useThirdPartyScripts";

const delay = 350;
const staticLoaderDelay = 1000;
const lowPriorityContentDelay = 6000;

const EasyFlowProvider = () => {
  const [loadLowPriorityContent, setLoadLowPriorityContent] = useState(false);
  const [thirdPartyScriptsLoaded, setThirdPartyScriptsLoaded] = useState(false);
  const [appContentDisplayed, setAppContentDisplayed] = useState(false);
  const { loadClarityScript, loadSentryScript } = useThirdPartyScripts();

  const displayAppContent = useCallback(() => {
    console.log("Displaying app content...");
    if (appContentDisplayed) return;
    const staticLoaderElement = document.getElementById("static-loader");
    const appElement = document.getElementById("easyflow-wrapper");
    if (appElement) {
      appElement.style.transition = "opacity 1s ease-in-out, visibility 1s ease-in-out";
      appElement.style.translate = "none";
      appElement.style.zIndex = "1";
      appElement.style.opacity = "1";
      appElement.style.position = "initial";
    }

    setTimeout(() => {
      if (staticLoaderElement) {
        staticLoaderElement.style.transition = "opacity 1s ease-in-out, visibility 1s ease-in-out";
        staticLoaderElement.style.opacity = "0";
        staticLoaderElement.style.zIndex = "-9";
      }
      // eslint-disable-next-line no-unused-expressions
      appElement && appElement.click();
    }, delay);
    setTimeout(() => staticLoaderElement?.remove(), staticLoaderDelay);
    setTimeout(() => setLoadLowPriorityContent(true), lowPriorityContentDelay);
    setAppContentDisplayed(true);
    console.log("Done");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTimeout(displayAppContent, delay);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!loadLowPriorityContent || thirdPartyScriptsLoaded) return;
    loadClarityScript();
    loadSentryScript();
    setThirdPartyScriptsLoaded(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadLowPriorityContent, thirdPartyScriptsLoaded]);

  return null;
};

export default EasyFlowProvider;
