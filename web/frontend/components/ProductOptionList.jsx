import { array, arrayOf, bool, func, object } from 'prop-types';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import {
  BlockStack,
  Button,
  Filters,
  Icon,
  InlineStack,
  ResourceList,
  Text,
} from '@shopify/polaris';
import { DragHandleIcon } from '@shopify/polaris-icons';

import OptionListTableView from './OptionListTableView';

/** @typedef {'Option' | 'Option set'} ItemType */

/**
 * @typedef {{
 *   id: string,
 *   itemId: number,
 *   type: string,
 *   title: string,
 *   subtitle: string,
 *   optionsText: string,
 *   productsText: string,
 *   optionTypeText: string,
 *   valuesText: string,
 *   onClick: () => void,
 *  }} ProductOptionItem
 */

/**
 * @param {number} value
 * @param {string} name
 */
function getValueDescription(value, name) {
  return `${value} ${value > 1 ? `${name}s` : name}`;
}

/**
 * @param {{
 *   optionSets?: IProductOptionSet[],
 *   options: IProductOption[],
 *   isLoading?: boolean,
 *   isMoreResultsLoading?: boolean,
 *   isPlain?: boolean,
 *   isReorderable?: boolean,
 *   isLimitReached?: boolean,
 *   onReorder?: (fromIndex: number, toIndex: number) => void,
 *   onOptionEdit?: (option: Partial<IProductOption>) => void,
 *   onOptionSetEdit?: (optionSet: Partial<IProductOptionSet>) => void,
 *   onDuplicate?: (optionSetIds: number[], optionIds: number[]) => void,
 *   onDelete?: (optionSetIds: number[], optionIds: number[]) => void,
 *   onSelectionChange?: (selectedItemIds: string[]) => void,
 *   itemActions?: { label?: string, icon?: React.FunctionComponent<React.SVGProps<SVGSVGElement>>, action: (itemId: string) => void }[],
 * }} props
 */
export default function ProductOptionList({
  optionSets,
  options,
  isLoading,
  isMoreResultsLoading,
  isPlain,
  isReorderable,
  isLimitReached,
  onReorder,
  onOptionEdit,
  onOptionSetEdit,
  onDuplicate,
  onDelete,
  onSelectionChange,
  itemActions,
}) {
  const [selectedItemIds, setSelectedItemIds] = useState(
    /** @type {Exclude<SelectedItems, 'All'>} */ ([]),
  );

  const [titleFilter, setTitleFilter] = useState('');
  const [sortSelected, setSortSelected] = useState(
    /** @type {{ sortBy: string, isDescending: boolean }} */ ({
      sortBy: 'type',
      isDescending: true,
    }),
  );
  const [typeFilter, setTypeFilter] = useState(/** @type {ItemType[]} */ ([]));

  const isSelectionEnabled = !isPlain || !!onSelectionChange;

  const handleSelectionChange = useCallback(
    (/** @type {string[]} */ newSelectedIds) => {
      setSelectedItemIds(newSelectedIds);
      onSelectionChange?.(newSelectedIds);
    },
    [onSelectionChange],
  );

  useEffect(() => {
    const newSelectedItemIds = selectedItemIds.filter(
      (id) =>
        optionSets?.some((optionSet) => `option-set-${optionSet.id}` === id) ||
        options.some((option) => `option-${option.id}` === id),
    );

    if (selectedItemIds.length !== newSelectedItemIds.length) {
      setSelectedItemIds(newSelectedItemIds);
    }
  }, [options, optionSets, selectedItemIds]);

  /** @type {ProductOptionItem[]} */
  const items = useMemo(
    () => [
      ...[
        ...(!typeFilter.length || typeFilter.includes('Option set')
          ? optionSets?.map((optionSet) => {
              const { id, title, optionIds, productIds, productSelectionMethod } = optionSet;

              const itemId = `option-set-${id}`;

              return {
                id: itemId,
                itemId: id,
                type: 'Option set',
                title,
                optionsText: getValueDescription(optionIds.length, 'option'),
                productsText:
                  productSelectionMethod === 'manual'
                    ? getValueDescription(productIds.length, 'product')
                    : 'Auto',
                optionTypeText: '-',
                valuesText: '-',
                subtitle: [
                  ...(optionIds.length ? [getValueDescription(optionIds.length, 'option')] : []),
                  ...(productIds.length ? [getValueDescription(productIds.length, 'product')] : []),
                ].join(' | '),
                onClick: () =>
                  onOptionSetEdit
                    ? onOptionSetEdit(optionSet)
                    : handleSelectionChange([...selectedItemIds, itemId]),
              };
            }) || []
          : []),
        ...(!typeFilter.length || typeFilter.includes('Option')
          ? options.map((option) => {
              const { id, title, nickname, type, values, productIds } = option;

              const itemId = `option-${id}`;

              return {
                id: itemId,
                itemId: id,
                type: 'Option',
                title: nickname || title,
                optionsText: '-',
                productsText: '-',
                optionTypeText: type,
                valuesText: getValueDescription(values.length, 'value'),
                subtitle: [
                  type,
                  ...(values.length ? [getValueDescription(values.length, 'value')] : []),
                  ...(productIds.length ? [getValueDescription(productIds.length, 'product')] : []),
                ].join(' | '),
                onClick: () =>
                  onOptionEdit
                    ? onOptionEdit(option)
                    : handleSelectionChange([...selectedItemIds, itemId]),
              };
            })
          : []),
      ]
        .filter(({ title }) => title.toLowerCase().includes(titleFilter.toLowerCase()))
        .sort(
          (a, b) =>
            (sortSelected.isDescending ? -1 : 1) *
            // @ts-ignore
            a[sortSelected.sortBy].localeCompare(b[sortSelected.sortBy]),
        ),
      ...(isMoreResultsLoading
        ? [
            {
              id: 'loading',
              itemId: -1,
              type: 'Option',
              title: 'loading...',
              optionsText: '',
              productsText: '',
              optionTypeText: '',
              valuesText: '',
              subtitle: '',
              onClick: () => {},
            },
          ]
        : []),
    ],
    [
      sortSelected,
      optionSets,
      options,
      isMoreResultsLoading,
      typeFilter,
      titleFilter,
      onOptionEdit,
      onOptionSetEdit,
      selectedItemIds,
      handleSelectionChange,
    ],
  );

  const optionList = !isPlain ? (
    <OptionListTableView
      isLimitReached={isLimitReached}
      items={items}
      isLoading={isLoading}
      titleFilter={titleFilter}
      setTitleFilter={setTitleFilter}
      setTypeFilter={setTypeFilter}
      onOptionSetEdit={onOptionSetEdit}
      onDuplicate={onDuplicate}
      onDelete={onDelete}
      sortSelected={sortSelected}
      setSortSelected={setSortSelected}
    />
  ) : (
    <ResourceList
      resourceName={{ singular: 'option', plural: 'options' }}
      loading={isLoading}
      flushFilters
      filterControl={
        isSelectionEnabled && (
          <Filters
            queryPlaceholder="Search options"
            queryValue={titleFilter}
            filters={[]}
            onQueryChange={setTitleFilter}
            onQueryClear={() => setTitleFilter('')}
            onClearAll={() => setTitleFilter('')}
          />
        )
      }
      items={items}
      renderItem={({ id, title, subtitle, onClick }, _, index) => {
        const media = isReorderable ? <Icon source={DragHandleIcon} /> : undefined;

        const resourceItem = (
          <ResourceList.Item
            id={id}
            onClick={onClick}
            media={media}
            accessibilityLabel={`View details for ${title}`}
          >
            <InlineStack align="space-between" blockAlign="center" gap="200">
              <BlockStack>
                <Text as="h3" variant="bodyMd" fontWeight="bold">
                  {title}
                </Text>

                <div>{subtitle}</div>
              </BlockStack>

              <InlineStack gap="200">
                {itemActions?.map(({ label, icon, action }) => (
                  // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                  <div
                    key={label || icon?.displayName}
                    onClick={(event) => event.stopPropagation()}
                  >
                    <Button icon={icon} onClick={() => action(id)}>
                      {label}
                    </Button>
                  </div>
                ))}
              </InlineStack>
            </InlineStack>
          </ResourceList.Item>
        );

        return isReorderable ? (
          <Draggable key={id} draggableId={id} index={index}>
            {(draggableProvided) => (
              <div
                ref={draggableProvided.innerRef}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...draggableProvided.draggableProps}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...draggableProvided.dragHandleProps}
              >
                {resourceItem}
              </div>
            )}
          </Draggable>
        ) : (
          resourceItem
        );
      }}
      selectable={isSelectionEnabled}
      selectedItems={selectedItemIds}
      onSelectionChange={(newSelectedIds) => {
        const selections = newSelectedIds === 'All' ? items.map((item) => item.id) : newSelectedIds;

        setSelectedItemIds(selections);
        onSelectionChange?.(selections);
      }}
    />
  );

  return isReorderable ? (
    <DragDropContext
      onDragEnd={({ source, destination }) => {
        if (destination) {
          onReorder?.(source.index, destination.index);
        }
      }}
    >
      <Droppable droppableId="droppable">
        {(provided) => (
          // eslint-disable-next-line react/jsx-props-no-spreading
          <div ref={provided.innerRef} {...provided.droppableProps}>
            {optionList}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  ) : (
    optionList
  );
}

ProductOptionList.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  optionSets: array,
  // eslint-disable-next-line react/forbid-prop-types
  options: array.isRequired,
  isLoading: bool,
  isMoreResultsLoading: bool,
  isPlain: bool,
  isReorderable: bool,
  isLimitReached: bool,
  onReorder: func,
  onOptionEdit: func,
  onOptionSetEdit: func,
  onDuplicate: func,
  onDelete: func,
  onSelectionChange: func,
  // eslint-disable-next-line react/forbid-prop-types
  itemActions: arrayOf(object),
};
