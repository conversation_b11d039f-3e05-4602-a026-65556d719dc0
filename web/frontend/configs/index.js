export default {
  themeAppExtensionAppBlockId: process.env.VITE_THEME_APP_EXTENSION_APP_BLOCK_ID,
  themeAppExtensionAppBlockUrl: `shopify://apps/product-options/blocks/app-block/${process.env.VITE_THEME_APP_EXTENSION_APP_BLOCK_ID}`,
  appBlockDeepLinkPath: `/themes/current/editor?template=product&addAppBlockId=${process.env.VITE_THEME_APP_EXTENSION_APP_BLOCK_ID}/app-block&target=mainSection`,
  appHandle: process.env.VITE_APP_HANDLE || "product-options-35",
  loadClarityScript: process.env.VITE_LOAD_CLARITY_SCRIPT || false,
  loadSentryScript: process.env.VITE_LOAD_SENTRY_SCRIPT || "false", // Default to true unless explicitly disabled
  sentryDNS: process.env.VITE_SENTRY_DNS || "https://<EMAIL>/3",
};
