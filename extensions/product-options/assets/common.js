"use strict";

(function () {
  /**
   * @typedef {HTMLInputElement | HTMLOptionElement | HTMLSelectElement
   *   | HTMLTextAreaElement} InputElement
   */

  // NOTE: Commented out because we might need to add back this later
  // Check if the current page is the product page, otherwise don't do anything.
  // const typeMeta = document.querySelector('meta[property="og:type"]');
  // const metaType = typeMeta && typeMeta.getAttribute('content');

  // if (metaType !== 'product') {
  //   return;
  // }

  const productOptionsId = "product-options-ho4ed2r6cwo";
  const appEmbedProductOptionsElement = document.getElementById(`${productOptionsId}-embed`);

  // We include the script in both app-embed.liquid and app-embed-global.liquid
  // so we need to make sure that we only run the script once
  if (window.Shopify.optionScriptInitialized) {
    if (appEmbedProductOptionsElement) {
      appEmbedProductOptionsElement.remove();
    }

    return;
  }

  window.Shopify.optionScriptInitialized = true;

  const originalXhrSend = window.XMLHttpRequest.prototype.send;
  const originalFetch = fetch;

  const shopifyRootRoute = window.Shopify.routes.root;

  // Parse rules
  const productOptionRulesElement = document.getElementById("ef__product-option-rules");
  const productOptionRulesText = productOptionRulesElement && productOptionRulesElement.textContent;

  /** @type {IProductOptionSet['rules']} */
  const productOptionRules = JSON.parse(productOptionRulesText || "[]");

  // Prase variant data
  const productVariantsElement = document.getElementById("ef__product-variant-data");
  const productVariantsText = productVariantsElement && productVariantsElement.textContent;

  /**
   * @type {{
   *   options: string[],
   *   variants: Pick<
   *     import('../productVariant').ProductVariant,
   *     'id' | 'options' | 'available' | 'price' | 'compare_at_price'
   *   >[]
   * } | null}
   */
  const productVariantData = productVariantsText && JSON.parse(productVariantsText);

  // Parse customizations
  const productOptionCustomizationsElement = document.getElementById("ef__product-option-customizations");

  const productOptionCustomizationsText =
    productOptionCustomizationsElement && productOptionCustomizationsElement.textContent;

  /**
   * @type {typeof import('../../../web/defaults').defaultStyleOptions & LegacyCustomizationFields}
   */
  const productOptionCustomizations = JSON.parse(productOptionCustomizationsText || "[]");

  const regularPriceItemSelector =
    productOptionCustomizations.regularPriceItemSelector ||
    "product-info .price-item--sale, product-info :not(.price__sale) .price-item--regular, .product-information .price ins .amount";

  const compareAtPriceItemSelector =
    productOptionCustomizations.compareAtPriceItemSelector ||
    "product-info .price__sale .price-item--regular, .product-information .price del .amount";

  const priceItemSelector = `${regularPriceItemSelector}, ${compareAtPriceItemSelector}`;

  // Use this for detecting if the app embed is enabled
  // (the other elemens are not in the DOM at this point yet)
  // const appEmbedScriptElement = document.querySelector(
  //   'script[src*="easyflow-product-options"][src$="/assets/app-embed.js"], script[src^="/assets/app-embed.js"]' +
  //     ', script[src*="ab26a2b1-616f-4b8c-b7b2-e74f83f39d5d"][src*="/assets/app-embed.js"]',
  // );

  // added by rumi - after name changed
  const appEmbedScriptElement = document.querySelector(
    'script[src*="easyflow-product-options"][src$="/assets/app-embed.js"], ' +
      'script[src^="/assets/app-embed.js"], ' +
      'script[src*="ab26a2b1-616f-4b8c-b7b2-e74f83f39d5d"][src*="/assets/app-embed.js"], ' +
      'script[src*="easyflow"][src$="/assets/app-embed.js"],' +
      'script[src*="314a75c1-8212-470a-98f1-03d96259764f"][src*="/assets/app-embed.js"]'
  );

  // TODO: Change syntax to ES5 so that Shopify can minify the script
  // TODO: Rewrite so that only one options block is possible

  let isUsingAppBlock = true;

  let productOptionsElement = document.getElementById(productOptionsId);

  if (!productOptionsElement) {
    productOptionsElement = appEmbedProductOptionsElement;

    // Change the ID to the actual ID
    if (productOptionsElement) {
      productOptionsElement.id = productOptionsId;
    }

    // Don't show the app embed outside of the product page.
    if (window.location.pathname.indexOf("/products/") === -1) {
      if (productOptionsElement) {
        productOptionsElement.remove();
      }

      return;
    }

    isUsingAppBlock = false;
  }

  if (!productOptionsElement) {
    throw new Error("Product options element not found");
  }

  // Recalculate prices using current currency's exchange rates

  [].forEach.call(
    productOptionsElement.querySelectorAll("[data-value-price]"),
    function (/** @type {Element} */ element) {
      const price = element.getAttribute("data-value-price");

      if (price) {
        element.setAttribute(
          "data-value-price",
          (
            parseFloat(price) *
            (!productOptionCustomizations.isGetPricesOnStorefront ? window.Shopify.currency.rate : 1)
          ).toString()
        );
      }
    }
  );

  [].forEach.call(
    productOptionsElement.querySelectorAll(".ef__option-value-price, .ef__option-selected-values-price"),
    function (/** @type {Element} */ element) {
      if (element.textContent) {
        const price = parseFloat(element.textContent) / 100;

        // eslint-disable-next-line no-param-reassign
        element.textContent = price
          ? ` (+${formatCurrency(
              price * (!productOptionCustomizations.isGetPricesOnStorefront ? window.Shopify.currency.rate : 1)
            )})`
          : "";
      }
    }
  );

  // Dropdown addon prices need to be calculated differently, because `option` tags don't allow
  // HTML tags inside them (so price is directly appended to the option value)
  [].forEach.call(
    productOptionsElement.getElementsByClassName("ef__option-value-dropdown"),
    function (/** @type {Element} */ element) {
      const priceText = element.getAttribute("data-value-price");
      const price = priceText ? parseFloat(priceText) / 100 : undefined;

      if (price) {
        // eslint-disable-next-line no-param-reassign
        element.textContent += ` (+${formatCurrency(price)})`;
      }
    }
  );

  // Replace the original Buy Now button with a new one

  /** @type {HTMLElement[] | HTMLCollectionOf<Element>} */
  let pageContainers = document.getElementsByTagName("main");
  pageContainers = pageContainers.length ? pageContainers : [document.body];

  if (
    !(
      productOptionCustomizations.isBuyNowIntegrationDisabled ||
      productOptionCustomizations.is_buy_now_integration_disabled
    ) &&
    productOptionsElement.firstElementChild &&
    productOptionsElement.firstElementChild.children.length
  ) {
    // There are instances that the button is already added to the page before the script runs
    const initialBuyNowButton = document.querySelector(
      '[data-testid="upstream-button"], .shopify-payment-button__button--unbranded'
    );

    overrideBuyNowButton(initialBuyNowButton);

    const observer = new MutationObserver(function (mutationList) {
      mutationList.forEach(function (mutation) {
        [].forEach.call(mutation.addedNodes, function (/** @type {Node} */ addedNode) {
          const buyNowButton =
            addedNode.parentElement &&
            addedNode.parentElement.querySelector(
              '[data-testid="upstream-button"], .shopify-payment-button__button--unbranded'
            );

          overrideBuyNowButton(buyNowButton);
        });
      });
    });

    [].forEach.call(pageContainers, function (observedNode) {
      observer.observe(observedNode, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeOldValue: true,
        characterData: true,
      });
    });
  }

  // Add product options to the product page

  if (isUsingAppBlock) {
    populateOptions([]);
  } else {
    const customSelector =
      productOptionCustomizations.appEmbedPlacementSelector || productOptionCustomizations.app_embed_placement_selector;

    // Try to add product options before the installment form (or using the selector override)...
    let afterElements = /** @type {HTMLElement[] | NodeListOf<Element>} */ (
      [].map.call(
        document.querySelectorAll(customSelector || "form.installment"),
        function (/** @type {Element} */ element) {
          return customSelector ? element : element.parentElement;
        }
      )
    );

    // ... if that doesn't exist, look for another anchor (e.g., quantity or info form) ...
    afterElements = afterElements.length
      ? afterElements
      : document.querySelectorAll(
          ".product-form__quantity, .form-field.product-quantity, .purchase-details__quantity, .product-form__info-list, .product-detail__form__action"
        );

    // ... if that also doesn't exist, add it to before the product form
    // (where the add to cart button is)
    afterElements = /** @type {HTMLElement[] | NodeListOf<Element>} */ (
      afterElements.length
        ? afterElements
        : [].map.call(document.getElementsByTagName("product-form"), function (/** @type {Element} */ element) {
            return element.parentElement;
          })
    );

    if (afterElements.length) {
      populateOptions(afterElements);
      productOptionsElement.remove();
    } else {
      const observer = new MutationObserver(function (mutationList) {
        /** @type {Node[]} */
        const addedNodes = [];

        mutationList.forEach(function (mutation) {
          [].forEach.call(mutation.addedNodes, function (node) {
            addedNodes.push(node);
          });
        });

        /** @type {HTMLElement | undefined} */
        let afterElement;

        for (const node of addedNodes) {
          if (node instanceof HTMLElement) {
            if (customSelector) {
              if (node.matches(customSelector)) {
                afterElement = node;
                break;
              }
            } else {
              if (node.tagName === "FORM" && node.classList.contains("installment") && node.parentElement) {
                afterElement = node.parentElement;
                break;
              }

              if (
                node.classList.contains("product-form__quantity") ||
                (node.classList.contains("form-field") && node.classList.contains("product-quantity")) ||
                node.classList.contains("purchase-details__quantity") ||
                node.classList.contains("product-form__info-list") ||
                node.classList.contains("product-detail__form__action")
              ) {
                afterElement = node;
                break;
              }

              if (node.tagName === "PRODUCT-FORM" && node.parentElement) {
                afterElement = node.parentElement;
                break;
              }
            }
          }
        }

        if (afterElement) {
          populateOptions([afterElement]);
          observer.disconnect();
        }
      });

      [].forEach.call(pageContainers, function (observedNode) {
        observer.observe(observedNode, {
          attributes: true,
          childList: true,
          subtree: true,
          attributeOldValue: true,
          characterData: true,
        });
      });
    }
  }

  window.EasyFlowHelpers.initialBlock = window.EasyFlowHelpers.getOriginalOptionsBlock();

  // Override fetch to intercept cart updates

  let isCartRequestMade = false;

  if (typeof fetch === "function") {
    window.fetch = function () {
      // TODO: check if variant ID is the one we have options for
      let url = arguments[0] instanceof URL ? arguments[0] : new URL(arguments[0], window.location.href);
      url = arguments[0] instanceof Request ? new URL(arguments[0].url, window.location.href) : url;

      if (
        url.origin === window.location.origin &&
        (url.pathname === `${shopifyRootRoute}cart/add` || url.pathname === `${shopifyRootRoute}cart/add.js`) &&
        ((!arguments[1] && arguments[0] instanceof Request) ||
          arguments[1].body instanceof FormData ||
          typeof arguments[1].body === "string")
      ) {
        isCartRequestMade = true;

        const originalThis = this;
        const originalArguments = arguments;

        return window.EasyFlowProductOptions.parseFetchBody(originalArguments).then(function (body) {
          /** @type {ReturnType<typeof changeCartItems>} */
          let modifiedCartData;

          try {
            modifiedCartData = changeCartItems(body);
          } catch (error) {
            return new Promise(function (_, reject) {
              reject(error);
            });
          }

          if (originalArguments[0] instanceof Request) {
            // Remove the `Content-Type` header to allow the browser to set it automatically
            // (necessary for the `FormData` boundaries to be correctly set)
            originalArguments[0].headers.delete("Content-Type");

            originalArguments[0] = new Request(originalArguments[0], {
              headers: originalArguments[0].headers,
              body: modifiedCartData.modifiedAddToCartData,
            });
          } else {
            originalArguments[1].body = modifiedCartData.modifiedAddToCartData;
          }

          if (modifiedCartData.addonItems.length) {
            return originalFetch(url.pathname, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ items: modifiedCartData.addonItems }),
            }).then(function () {
              return originalFetch
                .apply(originalThis, /** @type {any} */ (originalArguments))
                .then(function (response) {
                  // for reload options block after add to cart
                  setTimeout(() => {
                    window.EasyFlowHelpers.reloadOptionsBlockInitialized = true;
                    window.EasyFlowHelpers.reloadOptionsBlock();
                  }, 100);
                  return response;
                });
            });
          }
          return originalFetch.apply(originalThis, /** @type {any} */ (originalArguments)).then(function (response) {
            // for reload options block after add to cart
            setTimeout(() => {
              window.EasyFlowHelpers.reloadOptionsBlockInitialized = true;
              window.EasyFlowHelpers.reloadOptionsBlock();
            }, 100);
            return response;
          });
        });
      }
      return originalFetch.apply(this, /** @type {any} */ (arguments));
    };
  }

  // Override XMLHttpRequest to intercept cart updates.

  window.XMLHttpRequest.prototype.send = function () {
    // @ts-ignore
    // eslint-disable-next-line no-underscore-dangle
    const url = new URL(this._url, window.location.href);

    if (
      url.origin === window.location.origin &&
      (url.pathname === `${shopifyRootRoute}cart/add` || url.pathname === `${shopifyRootRoute}cart/add.js`) &&
      (arguments[0] instanceof FormData || typeof arguments[0] === "string")
    ) {
      isCartRequestMade = true;

      const modifiedCartData = changeCartItems(arguments[0]);
      arguments[0] = modifiedCartData.modifiedAddToCartData;

      const originalThis = this;
      const originalArguments = arguments;

      if (modifiedCartData.addonItems.length) {
        const request = new XMLHttpRequest();

        request.addEventListener("load", function () {
          originalXhrSend.apply(originalThis, /** @type {any} */ (originalArguments));
        });

        request.open("POST", url.pathname);
        request.setRequestHeader("Content-Type", "application/json");
        originalXhrSend.apply(request, [JSON.stringify({ items: modifiedCartData.addonItems })]);
        // for reload options block after add to cart
        setTimeout(() => {
          window.EasyFlowHelpers.reloadOptionsBlockInitialized = true;
          window.EasyFlowHelpers.reloadOptionsBlock();
        }, 100);
        return;
      }
    }

    originalXhrSend.apply(this, /** @type {any} */ (arguments));
  };

  // Override form submit to intercept cart updates.

  /**
   * @param {Event} event
   */
  function handleAddToCartFormSubmitted(event) {
    event.preventDefault();
    event.stopPropagation();
    // event.stopImmediatePropagation(); // Prevent other submit listeners from firing

    console.log("handleAddToCartFormSubmitted");
    console.log("shopifyRootRoute", shopifyRootRoute);

    setTimeout(function () {
      if (isCartRequestMade) {
        return;
      }

      const addToCartForm = /** @type {HTMLFormElement} */ (event.target);

      try {
        const formData = new FormData(/** @type {HTMLFormElement} */ (addToCartForm));
        const modifiedCartData = changeCartItems(formData);
        console.log("modifiedCartData", modifiedCartData);

        // Append the additional data to the form
        if (modifiedCartData.modifiedAddToCartData instanceof FormData) {
          Array.from(modifiedCartData.modifiedAddToCartData).forEach(function ([key, value]) {
            if (!formData.has(key)) {
              const inputElement = document.createElement("input");

              inputElement.setAttribute("type", "hidden");
              inputElement.setAttribute("name", key);
              inputElement.setAttribute("value", String(value));

              addToCartForm.appendChild(inputElement);
            }
          });
        }

        if (modifiedCartData.addonItems.length) {
          console.log("modifiedCartData.addonItems", modifiedCartData.addonItems);
          originalFetch(`${shopifyRootRoute}cart/add`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ items: modifiedCartData.addonItems }),
          }).then(function () {
            addToCartForm.submit();
          });
        } else {
          addToCartForm.submit();
        }
      } catch (error) {
        console.log("error", error);
        // addToCartForm.submit();
      }
    }, 0);
  }

  [].forEach.call(
    document.querySelectorAll(
      `form[action^="${shopifyRootRoute}cart/add"], form[action^="${window.location.origin + shopifyRootRoute}cart/add"]`
    ),
    function (/** @type {Element} */ form) {
      form.addEventListener("submit", handleAddToCartFormSubmitted);
    }
  );

  // The DOM can be modified after the submit listener is added, so we need to check it again
  window.addEventListener("DOMContentLoaded", function () {
    [].forEach.call(
      document.querySelectorAll(
        `form[action="${shopifyRootRoute}cart/add"], form[action^="${window.location.origin + shopifyRootRoute}cart/add"]`
      ),
      function (/** @type {Element} */ form) {
        form.addEventListener("submit", handleAddToCartFormSubmitted);
      }
    );
  });

  /* ------------------------------------- Helper functions ------------------------------------- */

  /**
   * @param {HTMLElement[] | NodeListOf<Element>} anchorElements
   */
  function populateOptions(anchorElements) {
    const afterElement = !isUsingAppBlock && anchorElements.length ? anchorElements[0] : null;

    // If we are using the app block, we will use the original element
    const clonedOptions = !isUsingAppBlock
      ? (productOptionsElement && productOptionsElement.cloneNode(true)) || null
      : productOptionsElement;

    if (clonedOptions instanceof HTMLElement) {
      // If there are no options, we don't need to do anything
      if (!clonedOptions.firstElementChild || !clonedOptions.firstElementChild.children.length) {
        return;
      }

      clonedOptions.removeAttribute("style");

      // If we insert the element into a form, remove our form element
      if (afterElement && afterElement.parentElement && afterElement.parentElement.closest("form")) {
        const formElement = clonedOptions.getElementsByTagName("form")[0];

        if (formElement.parentNode) {
          const children = document.createDocumentFragment();

          while (formElement.firstChild) {
            const child = formElement.removeChild(formElement.firstChild);
            children.appendChild(child);
          }

          formElement.parentNode.replaceChild(children, formElement);
        }
      }

      // Listen for input changes to apply the option set rules and calculate total
      [].forEach.call(
        clonedOptions.querySelectorAll(".ef__product-option-input, .select__select"),
        function (/** @type {InputElement} */ input) {
          input.addEventListener("input", function (event) {
            const isOptionVisibilityChanged = checkAndApplyRules(
              /** @type {InputElement} */ (event.target),
              clonedOptions,
              0
            );

            // If the visibility of the options changed, we need to enforce the maximum
            // selection count
            if (isOptionVisibilityChanged) {
              [].forEach.call(
                clonedOptions.querySelectorAll(".ef__option-values[data-option-maximum-selections]"),
                function (/** @type {Element} */ valuesContainer) {
                  const firstCheckbox = valuesContainer.querySelector('input[type="checkbox"]');

                  if (firstCheckbox) {
                    enforceSelectionCount(firstCheckbox, valuesContainer);
                  }
                }
              );
            }

            // Update the selected values text
            const option = input.closest(".ef__product-option");

            if (option && option.getAttribute("data-option-show-selected-values")) {
              const propertyValue = [].filter
                .call(option.getElementsByClassName("ef__product-option-input"), getValueValue)
                .map(function (/** @type {HTMLInputElement} */ inputElement) {
                  const valuePrice = parseFloat(inputElement.getAttribute("data-value-price") || "0") / 100;

                  return `${getValueTitle(inputElement)}${
                    valuePrice
                      ? `<span class="ef__option-selected-values-price"> (+${formatCurrency(valuePrice)})</span>`
                      : ""
                  }`;
                })
                .join(", ");

              const selectedValueElement = option.getElementsByClassName("ef__option-selected-values")[0];

              if (selectedValueElement) {
                selectedValueElement.innerHTML = propertyValue ? `- ${propertyValue}` : "";
              }
            }

            calculateTotal(clonedOptions);
          });
        }
      );

      // Listen for URL changes to apply the rules conditioned on Shopify options
      if (productVariantData) {
        const pushState = window.history.pushState;
        window.history.pushState = function () {
          const previousSearch = window.location.search;
          const result = pushState.apply(window.history, /** @type {any} */ (arguments));

          if (
            previousSearch !== window.location.search &&
            (window.location.search.includes("variant=") || window.location.search.includes("id="))
          ) {
            applyRules(clonedOptions, 0);
          }

          return result;
        };

        const replaceState = window.history.replaceState;
        window.history.replaceState = function () {
          const previousSearch = window.location.search;
          const result = replaceState.apply(window.history, /** @type {any} */ (arguments));

          if (
            previousSearch !== window.location.search &&
            (window.location.search.includes("variant=") || window.location.search.includes("id="))
          ) {
            applyRules(clonedOptions, 0);
          }

          return result;
        };

        window.addEventListener("popstate", function () {
          if (window.location.search.includes("variant=") || window.location.search.includes("id=")) {
            applyRules(clonedOptions, 0);
          }
        });
      }

      // Listen for file input changes to add upload behavior

      [].forEach.call(
        clonedOptions.querySelectorAll('input[type="file"]'),
        function (/** @type {Element} */ fileInput) {
          if (!(fileInput instanceof HTMLInputElement)) {
            return;
          }

          const deleteIcon = fileInput.parentElement && fileInput.parentElement.getElementsByTagName("svg")[0];

          const inputElement =
            fileInput.parentElement && fileInput.parentElement.getElementsByClassName("ef__upload-input")[0];

          const loadingElement =
            fileInput.parentElement && fileInput.parentElement.getElementsByClassName("ef__upload-loading")[0];

          const contentElement =
            fileInput.parentElement && fileInput.parentElement.getElementsByClassName("ef__upload-content")[0];

          const optionElemenet = fileInput.closest(".ef__product-option");
          const errorElement = optionElemenet && optionElemenet.getElementsByClassName("ef__product-option-error")[0];

          if (
            !deleteIcon ||
            !(inputElement instanceof HTMLElement) ||
            !(loadingElement instanceof HTMLElement) ||
            !(contentElement instanceof HTMLElement) ||
            !(errorElement instanceof HTMLElement)
          ) {
            return;
          }

          deleteIcon.addEventListener("click", function (event) {
            event.preventDefault();

            /* eslint-disable no-param-reassign */
            fileInput.value = "";
            fileInput.setAttribute("data-value", "");
            fileInput.disabled = false;
            /* eslint-enable no-param-reassign */

            contentElement.style.display = "none";
            inputElement.style.display = "";
          });

          fileInput.addEventListener("change", function () {
            const inputFile = fileInput.files && fileInput.files[0];
            if (!inputFile) {
              return;
            }

            const allowedFileTypes = fileInput.accept.split(", ");
            const fileExtension = inputFile.name.split(".").pop();

            if (
              allowedFileTypes.indexOf(inputFile.type) === -1 &&
              (!fileExtension ||
                (allowedFileTypes.indexOf(fileExtension) === -1 &&
                  allowedFileTypes.indexOf(`.${fileExtension}`) === -1))
            ) {
              errorElement.innerText = (
                productOptionCustomizations.fileTypeErrorText || "Only %file-types% files allowed."
              ).replace(/%file-types%/g, fileInput.accept);
              return;
            }

            if (inputFile.size > 20 * 1024 * 1024) {
              errorElement.innerText = (
                productOptionCustomizations.fileSizeErrorText || "File needs to be smaller than %max-file-size%."
              ).replace(/%max-file-size%/g, "20MB");
              return;
            }

            errorElement.innerText = "";

            // eslint-disable-next-line no-param-reassign
            fileInput.disabled = true;

            inputElement.style.display = "none";
            loadingElement.style.display = "";

            const fileUploadErrorText =
              productOptionCustomizations.fileUploadErrorText || "Failed to upload file: %reason%";

            // Generate upload URLs for the file
            originalFetch("/apps/appProxy/createUploadUrl", {
              method: "POST",
              headers: { "Content-Type": "application/json", Accept: "application/json" },
              body: JSON.stringify({
                file: { filename: inputFile.name, mimeType: inputFile.type },
              }),
            })
              .then(function (response) {
                errorElement.innerText =
                  response && !response.ok ? fileUploadErrorText.replace(/%reason%/g, response.statusText) : "";

                return response && response.ok && response.json();
              })
              .then(function (/** @type {CreateUploadUrlResponse} */ responseData) {
                if (!responseData || !responseData.data) {
                  return undefined;
                }

                /** @type {Record<string, string>} */
                const headers = {};

                responseData.data.parameters.forEach(function (data) {
                  headers[data.name] = data.value;
                });

                // Upload the file to the generated URL
                return originalFetch(responseData.data.url, {
                  method: "PUT",
                  headers: headers,
                  body: inputFile,
                }).then(function () {
                  const fileData = {
                    resourceUrl: responseData.data && responseData.data.resourceUrl,
                    filename: inputFile?.name,
                  };

                  // Create file for the store in Shopify
                  return originalFetch("/apps/appProxy/createUploadFile", {
                    method: "POST",
                    headers: { "Content-Type": "application/json", Accept: "application/json" },
                    body: JSON.stringify({
                      file: fileData,
                      contentType: fileInput.type.startsWith("image/") ? "IMAGE" : "FILE",
                    }),
                  });
                });
              })
              .then(function (response) {
                if (response) {
                  errorElement.innerText = !response.ok
                    ? fileUploadErrorText.replace(/%reason%/g, response.statusText)
                    : "";
                }

                return response && response.ok && response.json();
              })
              .then(function (/** @type {CreateUploadFileResponse} */ fileCreateData) {
                loadingElement.style.display = "none";

                const fileCreateResult = fileCreateData && fileCreateData.data;
                if (!fileCreateResult) {
                  inputElement.style.display = "";

                  // eslint-disable-next-line no-param-reassign
                  fileInput.disabled = false;

                  return;
                }

                fileInput.setAttribute("data-value", fileCreateResult.url);

                const imageElement = contentElement && contentElement.getElementsByTagName("img")[0];

                const textElement = contentElement && contentElement.getElementsByTagName("span")[0];

                if (!(imageElement instanceof HTMLImageElement) || !(textElement instanceof HTMLElement)) {
                  return;
                }

                contentElement.style.display = "";
                textElement.innerText = inputFile.name;

                if (inputFile.type.startsWith("image")) {
                  const reader = new FileReader();

                  reader.onload = function (event) {
                    if (event.target && typeof event.target.result === "string") {
                      imageElement.src = event.target.result;
                      imageElement.onclick = function () {
                        window.open(fileCreateResult.url, "_blank");
                      };

                      imageElement.style.display = "";
                    }
                  };

                  reader.readAsDataURL(inputFile);
                } else {
                  imageElement.style.display = "none";
                }

                loadingElement.style.display = "none";
              });
          });
        }
      );

      // Listen for checkbox changes to enforce maximum selection count
      [].forEach.call(
        clonedOptions.querySelectorAll('input[type="checkbox"]'),
        function (/** @type {Element} */ clickedCheckbox) {
          clickedCheckbox.addEventListener("click", function () {
            const optionValuesContainer = clickedCheckbox.closest(".ef__option-values");

            const isMultiSelect =
              optionValuesContainer && optionValuesContainer.getAttribute("data-option-multi-select") === "true";

            if (isMultiSelect) {
              enforceSelectionCount(clickedCheckbox, optionValuesContainer);
            } else {
              // If the option is not a multi-select, uncheck all other checkboxes
              const optionCheckboxes =
                optionValuesContainer && optionValuesContainer.querySelectorAll('input[type="checkbox"]');

              if (optionCheckboxes) {
                [].forEach.call(optionCheckboxes, function (/** @type {Element} */ checkbox) {
                  if (checkbox instanceof HTMLInputElement && checkbox !== clickedCheckbox) {
                    // eslint-disable-next-line no-param-reassign
                    checkbox.checked = false;
                  }
                });
              }
            }
          });
        }
      );

      // Listen for text box changes to enforce character limits
      [].forEach.call(
        clonedOptions.getElementsByClassName("ef__character-counter"),
        function (/** @type {HTMLElement} */ counter) {
          const textbox = counter.parentElement && counter.parentElement.querySelector("input, textarea");

          if (textbox instanceof HTMLInputElement || textbox instanceof HTMLTextAreaElement) {
            const maximumCharacters = parseInt(textbox.getAttribute("maxlength") || "0", 10);

            if (maximumCharacters) {
              textbox.addEventListener("input", function () {
                // eslint-disable-next-line no-param-reassign
                counter.textContent = `${textbox.value.length}/${maximumCharacters}`;
              });
            }
          }
        }
      );

      // Listen for number input changes to enforce minimum and maximum values
      [].forEach.call(
        clonedOptions.querySelectorAll('input[type="number"]'),
        function (/** @type {Element} */ numberInput) {
          if (!(numberInput instanceof HTMLInputElement)) {
            return;
          }

          numberInput.addEventListener("blur", function () {
            if (numberInput.value) {
              const minimumValue = numberInput.getAttribute("min");
              const maximumValue = numberInput.getAttribute("max");

              const value = parseInt(numberInput.value, 10);

              if (minimumValue && value < parseInt(minimumValue, 10)) {
                // eslint-disable-next-line no-param-reassign
                numberInput.value = minimumValue;
              } else if (maximumValue && value > parseInt(maximumValue, 10)) {
                // eslint-disable-next-line no-param-reassign
                numberInput.value = maximumValue;
              }
            }
          });
        }
      );

      // Listen for variant change to recalculate the total price
      const priceObserver = new MutationObserver(function (mutations) {
        let isPriceItemChanged = false;

        mutations.forEach(function (mutation) {
          isPriceItemChanged =
            isPriceItemChanged ||
            [].some.call(mutation.addedNodes, function (/** @type {Node} */ node) {
              return (
                (node instanceof HTMLElement &&
                  (node.querySelector(priceItemSelector) || node.matches(priceItemSelector))) ||
                (node.parentElement && node.parentElement.matches(priceItemSelector))
              );
            });
        });

        if (isPriceItemChanged) {
          calculateTotal(clonedOptions, true);
        }
      });

      [].forEach.call(pageContainers, function (/** @type {Element} */ element) {
        if (element.parentElement) {
          priceObserver.observe(element.parentElement, { childList: true, subtree: true });
        }
      });
    }

    // This applies to the app embed only
    // We will remove the original element and insert the cloned element
    if (!isUsingAppBlock && afterElement && afterElement.parentElement && clonedOptions) {
      if (productOptionsElement) {
        productOptionsElement.remove();
      }

      afterElement.parentElement.insertBefore(clonedOptions, afterElement);
    }

    // If there are global options or there are rules conditioned on shopify options,
    // we need to apply the rules, because initial hidden values cannot be calculated for them
    if (
      // TODO: Add this back and fix initial options not hiding properly
      // productOptionRulesElement &&
      // productOptionRulesElement.getAttribute('data-is-includes-global') &&
      // productVariantData
      clonedOptions instanceof HTMLElement
    ) {
      applyRules(clonedOptions, 0);
      calculateTotal(clonedOptions);
    }
  }

  /**
   * @param {Element | null} buyNowButton
   */
  function overrideBuyNowButton(buyNowButton) {
    // Override the "Buy now" button
    const addToCartForm = buyNowButton && buyNowButton.closest("form");
    const isCustomButton = buyNowButton && buyNowButton.getAttribute("data-is-custom-button");

    const clonedBuyNowButton = buyNowButton && buyNowButton.cloneNode(true);

    if (
      !buyNowButton ||
      !buyNowButton.parentNode ||
      !addToCartForm ||
      isCustomButton ||
      !(clonedBuyNowButton instanceof HTMLElement)
    ) {
      return;
    }

    clonedBuyNowButton.onclick = function () {
      const modifiedAddToCartData = changeCartItems(new FormData(addToCartForm));

      new Promise(function (resolve) {
        if (modifiedAddToCartData.addonItems.length) {
          originalFetch(`${shopifyRootRoute}cart/add?override=false`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ items: modifiedAddToCartData.addonItems }),
          }).then(function () {
            resolve(true);
          });
        } else {
          resolve(true);
        }
      })
        .then(function () {
          return originalFetch(`${shopifyRootRoute}cart/add?override=false`, {
            method: "POST",
            body: modifiedAddToCartData.modifiedAddToCartData,
          });
        })
        .then(function () {
          window.location.assign("/checkout");
        });
    };

    clonedBuyNowButton.setAttribute("data-is-custom-button", "true");
    buyNowButton.parentNode.replaceChild(clonedBuyNowButton, buyNowButton);

    // Override the "More payment options"
    const shopifyPaymentButton = clonedBuyNowButton.closest(".shopify-payment-button");

    const moreOptionsButton = shopifyPaymentButton
      ? shopifyPaymentButton.getElementsByClassName("shopify-payment-button__more-options")[0]
      : null;

    const clonedMoreOptionsButton = moreOptionsButton && moreOptionsButton.cloneNode(true);

    if (
      !clonedMoreOptionsButton ||
      !moreOptionsButton.parentElement ||
      !(clonedMoreOptionsButton instanceof HTMLElement)
    ) {
      return;
    }

    clonedMoreOptionsButton.onclick = function () {
      clonedBuyNowButton.click();
    };

    moreOptionsButton.parentElement.replaceChild(clonedMoreOptionsButton, moreOptionsButton);
  }

  /**
   * @param {Element} clickedCheckbox
   * @param {Element} valuesContainer
   */
  function enforceSelectionCount(clickedCheckbox, valuesContainer) {
    const maximumSelectionCount = valuesContainer.getAttribute("data-option-maximum-selections");

    if (!(clickedCheckbox instanceof HTMLInputElement) || !maximumSelectionCount) {
      return;
    }

    // If the maximum selection count is set, disable other checkboxes if the limit is reached
    const checkboxes = valuesContainer.querySelectorAll('input[type="checkbox"]');
    const selectionCount = checkboxes
      ? [].filter.call(checkboxes, function (/** @type {HTMLInputElement} */ checkbox) {
          return isValueVisible(checkbox) && checkbox.checked;
        }).length
      : 0;

    const isDisable = selectionCount >= parseInt(maximumSelectionCount, 10);

    [].forEach.call(checkboxes, function (/** @type {Element} */ checkbox) {
      if (checkbox instanceof HTMLInputElement && !checkbox.checked) {
        // eslint-disable-next-line no-param-reassign
        checkbox.disabled = isDisable || false;
      }
    });
  }

  /**
   * @param {InputElement} inputElement
   */
  function isValueVisible(inputElement) {
    /** @type {HTMLElement | null} */
    const optionElement = inputElement.closest(".ef__product-option");

    /** @type {HTMLElement | null} */
    const valueElement = inputElement.closest(".ef__option-value");

    return (
      valueElement && valueElement.style.display !== "none" && optionElement && optionElement.style.display !== "none"
    );
  }

  /**
   * @param {InputElement} element
   */
  function getValueTitle(element) {
    const title =
      element instanceof HTMLInputElement && (element.type === "checkbox" || element.type === "radio")
        ? element.getAttribute("data-value-title")
        : element.value;

    return element instanceof HTMLInputElement && element.type === "file" ? element.getAttribute("data-value") : title;
  }

  /**
   * @param {InputElement} element
   */
  function getValueValue(element) {
    if (!isValueVisible(element)) {
      return false;
    }

    let value =
      element instanceof HTMLInputElement && (element.type === "checkbox" || element.type === "radio")
        ? element.checked
        : element.value;

    value = element instanceof HTMLOptionElement ? element.selected : value;

    return element instanceof HTMLInputElement && element.type === "file"
      ? element.getAttribute("data-value") || false
      : value;
  }

  /**
   * @param {InputElement | null} target
   * @param {HTMLElement} clonedOptions
   * @param {number} recursionDepth
   */
  function checkAndApplyRules(target, clonedOptions, recursionDepth) {
    if (!productOptionRules || recursionDepth > 9) {
      return false;
    }

    // Check if the changed input is included in any rule
    const changedOptionId = target instanceof HTMLElement ? target.getAttribute("data-option-id") : null;

    const isRulesChanged = productOptionRules.some(function (rule) {
      return rule.conditions.some(function (condition) {
        return condition.isShopifyOption || condition.optionId.toString() === changedOptionId;
      });
    });

    if (target && !isRulesChanged) {
      return false;
    }

    return applyRules(clonedOptions, recursionDepth);
  }

  /**
   * @param {HTMLElement} clonedOptions
   * @param {number} recursionDepth
   */
  function applyRules(clonedOptions, recursionDepth) {
    if (!productOptionRules) {
      return false;
    }

    // Get option data from HTML properties

    /**
      @type {{
        [actionOptionId: string]: {
          title?: string,
          value: boolean | string,
          element: InputElement | HTMLElement,
        }[],
      }}
    */
    const valuesData = {};

    [].slice
      .call((clonedOptions && clonedOptions.getElementsByClassName("ef__product-option-input")) || [])
      .forEach(function (/** @type {InputElement} */ element) {
        const optionId = element.getAttribute("data-option-id");
        if (!optionId) {
          return;
        }

        if (!valuesData[optionId]) {
          valuesData[optionId] = [];
        }

        valuesData[optionId].push({
          title: element.getAttribute("data-value-title") || undefined,
          value: getValueValue(element),
          element: element,
        });
      });

    // Process HTML content elements
    [].slice
      .call((clonedOptions && clonedOptions.getElementsByClassName("ef__html-content")) || [])
      .forEach(function (/** @type {HTMLElement} */ element) {
        const optionId = element.getAttribute("data-option-id");
        if (!optionId) {
          return;
        }

        if (!valuesData[optionId]) {
          valuesData[optionId] = [];
        }

        valuesData[optionId].push({
          title: element.getAttribute("data-value-title") || undefined,
          value: true, // HTML content is always considered as having a value
          element: element,
        });
      });

    // Calculate all hidden options based on the rules

    /**
     * @type {{ [optionId: string]: string[] | 'All' }}
     */
    const optionHiddenValues = {};

    /** @type {Exclude<typeof productVariantData, null>['variants'][number] | undefined} */
    let currentVariantData;

    if (productVariantData) {
      const urlSearchParams = new URLSearchParams(window.location.search);
      const currentVariantId = parseInt(urlSearchParams.get("variant") || urlSearchParams.get("id") || "0", 10);

      currentVariantData = productVariantData.variants.filter(function (variant) {
        return currentVariantId ? variant.id === currentVariantId : variant.available;
      })[0];
    }

    productOptionRules.forEach(function (rule) {
      // Check if current rule matches based on selected values
      const isMatches = rule.conditions[rule.isAllConditionsRequired ? "every" : "some"](function (condition) {
        // Shopify option conditions
        if (condition.isShopifyOption) {
          const optionPosition =
            productVariantData && productVariantData.options.indexOf(condition.optionId.toString());

          const isIncludes =
            currentVariantData &&
            typeof optionPosition === "number" &&
            optionPosition !== -1 &&
            condition.values.indexOf(currentVariantData.options[optionPosition]) !== -1;

          return condition.relation === "in" ? isIncludes : !isIncludes;
        }

        // EasyFlow option conditions
        const selectedValues =
          valuesData[condition.optionId] &&
          /** @type {string[] | undefined} */ (
            valuesData[condition.optionId]
              .filter(function (valueData) {
                return valueData.value;
              })
              .map(function (valueData) {
                return valueData.title;
              })
          );

        // Single value conditions
        if (condition.values.indexOf("__any__") !== -1) {
          const isNotEmpty = selectedValues && selectedValues.length;
          return condition.relation === "in" ? isNotEmpty : !isNotEmpty;
        }

        return (
          selectedValues &&
          selectedValues.some(function (value) {
            const isIncludes = condition.values.indexOf(value) !== -1;
            return condition.relation === "in" ? isIncludes : !isIncludes;
          })
        );
      });

      // Go over each action and calculate for all values if they are hidden or not
      rule.actions.forEach(function (action) {
        const actionValues = valuesData[action.optionId];
        if (!actionValues || !actionValues.length) {
          return;
        }

        let hiddenValues = !actionValues.some(function (value) {
          return value.title;
        })
          ? "All"
          : action.values;

        hiddenValues =
          (action.type === "show" && !isMatches) || (action.type === "hide" && isMatches) ? hiddenValues : [];

        let previousHiddenValues = optionHiddenValues[action.optionId];

        if (previousHiddenValues === "All" || hiddenValues === "All") {
          optionHiddenValues[action.optionId] = "All";
          return;
        }

        if (!previousHiddenValues) {
          previousHiddenValues = [];
        }

        optionHiddenValues[action.optionId] = previousHiddenValues.concat(hiddenValues);
      });
    });

    // Hide/show values/options based on the previously calculated set of hidden values

    let isOptionVisibilityChanged = false;

    Object.keys(valuesData).forEach(function (optionId) {
      const hiddenValues = optionHiddenValues[optionId] || [];

      const values = /** @type {string[]} */ (
        valuesData[optionId]
          .map(function (value) {
            return value.title;
          })
          .filter(Boolean)
      );

      let isHideAll = false;

      if (
        hiddenValues === "All" ||
        (values.length &&
          values.every(function (value) {
            return hiddenValues.indexOf(value) !== -1;
          }))
      ) {
        isHideAll = true;
      }

      // Hide/show full option
      /** @type {HTMLElement | null} */
      const optionElement = valuesData[optionId][0].element.closest("[data-option-title]");

      if (optionElement) {
        const afterOptionVisibility = isHideAll ? "none" : "";

        if (afterOptionVisibility !== optionElement.style.display) {
          optionElement.style.display = afterOptionVisibility;

          // Safari fix: can't hide option, so we need to disable it
          if (optionElement instanceof HTMLOptionElement) {
            optionElement.disabled = afterOptionVisibility === "none";
          }

          isOptionVisibilityChanged = true;
        }
      }

      if (!isHideAll) {
        // Hide/show option values
        valuesData[optionId].forEach(function (value) {
          const inputContainer =
            value.element instanceof HTMLOptionElement ? value.element : value.element.parentElement;

          if (inputContainer) {
            const optionType = value.element.getAttribute("data-option-type");

            const displayValue =
              value.element instanceof HTMLInputElement &&
              (optionType === "Button" || optionType === "Image swatch" || optionType === "Color swatch")
                ? "inline-block"
                : "";

            const isShow = !value.title || hiddenValues.indexOf(value.title) === -1;

            const afterValueVisibility = !isShow ? "none" : displayValue;

            if (afterValueVisibility !== inputContainer.style.display) {
              inputContainer.style.display = afterValueVisibility;

              // Safari fix: can't hide option, so we need to disable it
              if (inputContainer instanceof HTMLOptionElement) {
                inputContainer.disabled = afterValueVisibility === "none";
              }

              isOptionVisibilityChanged = true;
            }

            // If we are hiding a select option, we need to update its value to a non-hidden one
            if (inputContainer instanceof HTMLOptionElement && !isShow) {
              const selectElement = /** @type {HTMLSelectElement} */ (inputContainer.parentElement);

              if (selectElement.value === value.title) {
                const firstVisibleOption =
                  selectElement.parentElement &&
                  /** @type {HTMLOptionElement[]} */ ([]).filter.call(
                    selectElement.parentElement.getElementsByTagName("OPTION"),
                    function (selectOptionElement) {
                      return selectOptionElement.style.display !== "none";
                    }
                  )[0];

                const newOptionIndex = firstVisibleOption && firstVisibleOption.index;

                if (newOptionIndex !== null) {
                  selectElement.selectedIndex = newOptionIndex;

                  // Some themes replace the dropdown and add a span for displaying a value.
                  // We need to overwrite that span's inner text as well to change the display value.
                  /** @type {HTMLElement | null} */
                  const customTextElement =
                    selectElement.parentElement && selectElement.parentElement.querySelector("span.value");

                  if (customTextElement && firstVisibleOption) {
                    customTextElement.innerText = firstVisibleOption.innerText.trim();
                  }
                }

                // Apply rules again with the new selection
                isOptionVisibilityChanged = true;
              }
            }
          }
        });
      }
    });

    // If one of the options' visibility changed, apply rules again
    if (isOptionVisibilityChanged) {
      checkAndApplyRules(null, clonedOptions, recursionDepth + 1);
      return true;
    }

    return false;
  }

  /**
   * @param {FormData | string} addToCartData
   */
  function changeCartItems(addToCartData) {
    // TODO: check if variant ID is the one we have options for
    /** @type {FormData} */
    let formData = new FormData();

    let isJSONData = false;

    if (typeof addToCartData === "string") {
      try {
        // `addToCartData` is a JSON string
        const parsedAddToCartData = JSON.parse(addToCartData);
        if ((!parsedAddToCartData.items || !parsedAddToCartData.items.length) && !parsedAddToCartData["product-id"]) {
          return { modifiedAddToCartData: addToCartData, addonItems: [] };
        }

        formData = window.EasyFlowProductOptions.objectToFormData(
          parsedAddToCartData.items ? parsedAddToCartData.items[0] : parsedAddToCartData
        );

        isJSONData = true;
      } catch (_) {
        // `addToCartData` is a query string
        new URLSearchParams(addToCartData).forEach(function (value, name) {
          formData.append(name, value);
        });
      }
    } else if (addToCartData instanceof FormData) {
      // Copy form data, so that we don't modify the original one
      [].forEach.call(Array.from(addToCartData), function (entry) {
        formData.append(entry[0], entry[1]);
      });
    } else {
      throw new Error("Invalid cart data type");
    }

    const currentOptionsInput = document.getElementById(productOptionsId);
    const options = currentOptionsInput && currentOptionsInput.getElementsByClassName("ef__product-option");

    const baseItemIdentifier = `${formData.get("id")}-${Math.random().toString(36).substring(7)}`;

    /** @type {Partial<CartItem>[]} */
    const addonItems = [];

    /** @type {Element | undefined} */
    let firstErroredOption;

    [].forEach.call(options || [], function (/** @type {HTMLElement} */ option) {
      if (option && option.style.display === "none") {
        return;
      }

      const propertyName = option.getAttribute("data-option-in-cart-name") || option.getAttribute("data-option-title");

      /** @type {InputElement[]} */
      const valueElements = [].filter.call(option.getElementsByClassName("ef__product-option-input"), getValueValue);

      const valueTitles = /** @type {string[]} */ (valueElements.map(getValueTitle).filter(Boolean));

      const propertyValue = valueTitles.join(", ");

      const errorElement = option.getElementsByClassName("ef__product-option-error")[0];

      if (errorElement instanceof HTMLElement) {
        const optionError = getOptionError(option, propertyValue, valueElements);
        errorElement.innerText = optionError || "";

        if (!firstErroredOption && optionError) {
          firstErroredOption = option;
        }
      }

      if (propertyName && propertyValue) {
        formData.append(`properties[${propertyName}]`, propertyValue);

        valueElements.forEach(function (valueElement) {
          const variantAttribute = valueElement.getAttribute("data-variant-id");

          const variantId = variantAttribute && Number(new URL(variantAttribute).pathname.split("/").slice(-1)[0]);

          const isMatchAddonQuantity =
            formData.has("quantity") &&
            (productOptionCustomizations.isMatchAddonQuantity || productOptionCustomizations.is_match_addon_quantity);

          if (variantId) {
            /** @type {Partial<CartItem>} */
            const addonItem = {
              id: variantId,
              quantity: isMatchAddonQuantity ? Number(formData.get("quantity")) : 1,
            };

            if (appEmbedScriptElement) {
              addonItem.properties = { _base_product_id: baseItemIdentifier };

              if (
                productOptionCustomizations.isEnableRemoveAddonFromCart ||
                productOptionCustomizations.is_enable_remove_addon_from_cart
              ) {
                // eslint-disable-next-line no-underscore-dangle
                addonItem.properties._property_name = propertyName;
                // eslint-disable-next-line no-underscore-dangle
                addonItem.properties._value_title = getValueTitle(valueElement) || undefined;

                if (valueTitles.length > 1) {
                  // eslint-disable-next-line no-underscore-dangle
                  addonItem.properties._value_titles = valueTitles;
                }
              }
            }

            addonItems.push(addonItem);
          }
        });
      }
    });

    if (firstErroredOption) {
      firstErroredOption.scrollIntoView({ behavior: "smooth" });

      const error = /** @type {Error & { description?: string }} */ (
        new Error("Required product option fields are missing")
      );

      throw error;
    }

    // Add a random base item identifier to relate addon items to the base item
    if (addonItems.length && appEmbedScriptElement) {
      formData.append("properties[_id]", baseItemIdentifier);
    }

    // Shopify adds the items to the cart in reverse, so we need to reverse it
    addonItems.reverse();

    /** @type {FormData | string} */
    let modifiedAddToCartData = formData;

    if (typeof addToCartData === "string") {
      if (isJSONData) {
        let parsedAddToCartData = JSON.parse(addToCartData);

        if (parsedAddToCartData.items) {
          parsedAddToCartData.items[0] = window.EasyFlowProductOptions.formDataToObject(formData);
        } else {
          parsedAddToCartData = window.EasyFlowProductOptions.formDataToObject(formData, parsedAddToCartData);
        }

        modifiedAddToCartData = JSON.stringify(parsedAddToCartData);
      } else {
        modifiedAddToCartData = new URLSearchParams(
          /** @type {[string, string][]} */ (Array.from(formData))
        ).toString();
      }
    }

    return { modifiedAddToCartData: modifiedAddToCartData, addonItems: addonItems };
  }

  /**
   * @param {HTMLElement} option
   * @param {string} propertyValue
   * @param {InputElement[]} valueElements
   */
  function getOptionError(option, propertyValue, valueElements) {
    if (option.getAttribute("data-is-required") && (!propertyValue || propertyValue.trim() === "")) {
      return (productOptionCustomizations.requiredOptionErrorText || '"%option-title%" is required.').replace(
        /%option-title%/g,
        option.getAttribute("data-option-title") || ""
      );
    }

    const inputElement = option.getElementsByClassName("ef__product-option-input")[0];
    const minimumValue = inputElement && inputElement.getAttribute("minlength");

    if (
      minimumValue &&
      valueElements[0] &&
      valueElements[0].value.length &&
      valueElements[0].value.length < parseInt(minimumValue, 10)
    ) {
      return (
        productOptionCustomizations.mimumCharacterErrorText ||
        '"%option-title%" requires at least %min-char-limit% characters.'
      )
        .replace(/%option-title%/g, option.getAttribute("data-option-title") || "")
        .replace(/%min-char-limit%/g, minimumValue);
    }

    const optionValuesElement = option.getElementsByClassName("ef__option-values")[0];

    const minimumSelectionCount =
      optionValuesElement && optionValuesElement.getAttribute("data-option-minimum-selections");

    const maximumSelectionCount =
      optionValuesElement && optionValuesElement.getAttribute("data-option-maximum-selections");

    if (minimumSelectionCount || maximumSelectionCount) {
      const selectedCount = valueElements.filter(function (element) {
        return element instanceof HTMLInputElement && isValueVisible(element) && element.checked;
      }).length;

      if (minimumSelectionCount && selectedCount && selectedCount < parseInt(minimumSelectionCount, 10)) {
        return (
          productOptionCustomizations.minimumSelectionErrorText ||
          '"%option-title%" requires at least %min-selection-limit% selections.'
        )
          .replace(/%option-title%/g, option.getAttribute("data-option-title") || "")
          .replace(/%min-selection-limit%/g, minimumSelectionCount);
      }

      if (maximumSelectionCount && selectedCount > parseInt(maximumSelectionCount, 10)) {
        return (
          productOptionCustomizations.maximumSelectionErrorText ||
          '"%option-title%" allows only %max-selection-limit% selections.'
        )
          .replace(/%option-title%/g, option.getAttribute("data-option-title") || "")
          .replace(/%max-selection-limit%/g, maximumSelectionCount);
      }
    }

    return undefined;
  }

  /* eslint-disable no-var, vars-on-top */

  /** @type {number | undefined} */
  var variantBasePrice;

  /** @type {number | undefined} */
  var variantCompareAtPrice;

  /* eslint-enable no-var */

  /**
   * @param {HTMLElement} clonedOptions
   * @param {boolean} [isBasePriceChange]
   */
  function calculateTotal(clonedOptions, isBasePriceChange = false) {
    const priceItem = document.querySelector(priceItemSelector);

    // Prevent detecting base price item change after the total price is recalculated
    if (priceItem && priceItem.getAttribute("data-ef-price-item-changed") === "true") {
      priceItem.removeAttribute("data-ef-price-item-changed");
      return;
    }

    // Get the base price and compare at price of the current variant
    if ((isBasePriceChange || variantBasePrice === undefined) && productVariantData) {
      const urlSearchParams = new URLSearchParams(window.location.search);
      const currentVariantId = parseInt(urlSearchParams.get("variant") || urlSearchParams.get("id") || "0", 10);

      const currentVariant = productVariantData.variants.filter(function (variant) {
        return currentVariantId ? variant.id === currentVariantId : variant.available;
      })[0];

      if (currentVariant) {
        variantBasePrice = currentVariant.price / 100;
        variantCompareAtPrice = (currentVariant.compare_at_price || currentVariant.price) / 100;
      }
    }

    let addonsTotal = 0;

    // Calculate the total price of the selected add-ons
    [].slice
      .call((clonedOptions && clonedOptions.getElementsByClassName("ef__product-option-input")) || [])
      .forEach(function (/** @type {InputElement} */ element) {
        if (getValueValue(element)) {
          const valuePrice = element.getAttribute("data-value-price");
          addonsTotal += valuePrice ? parseFloat(valuePrice) / 100 : 0;
        }
      });

    const totalElement = /** @type {HTMLElement} */ (document.getElementsByClassName("ef__options-addon-total")[0]);

    if (!totalElement) {
      return;
    }

    // Hide the total price if there are no add-ons or if the total price is 0
    if (
      !(productOptionCustomizations.isShowTotalPrice || productOptionCustomizations.is_show_total_price) ||
      addonsTotal <= 0
    ) {
      totalElement.style.display = "none";
    } else {
      totalElement.style.display = "";
    }

    // Set addon total elements' text
    const addonTotalAmountElements = totalElement.getElementsByClassName("ef__addon-amount");

    [].forEach.call(addonTotalAmountElements, function (/** @type {HTMLElement} */ element) {
      // eslint-disable-next-line no-param-reassign
      element.innerText = formatCurrency(addonsTotal);
    });

    // Base price is the price of the variant if set, otherwise the base price of the product
    let basePrice =
      (productOptionsElement ? parseInt(productOptionsElement.getAttribute("data-product-base-price") || "0", 10) : 0) /
      100;

    basePrice = variantBasePrice === undefined ? basePrice : variantBasePrice;

    const isBasePriceChangeEnabled =
      !productOptionCustomizations.isChangeBaseProductPriceDisabled &&
      !productOptionCustomizations.is_change_base_product_price_disabled;

    // Set the total price of the product with the add-ons
    const totalAmountElements = document.querySelectorAll(
      `.ef__total-amount${isBasePriceChangeEnabled ? `, ${priceItemSelector}` : ""}`
    );

    [].forEach.call(totalAmountElements, function (/** @type {HTMLElement} */ element) {
      // Set the compare at and base price texts with the appropriate amounts calculated above
      const priceWithoutAddons = element.matches(compareAtPriceItemSelector)
        ? variantCompareAtPrice || basePrice
        : basePrice;

      // eslint-disable-next-line no-param-reassign
      element.innerText = formatCurrency(priceWithoutAddons + addonsTotal);
    });

    if (priceItem && isBasePriceChangeEnabled) {
      priceItem.setAttribute("data-ef-price-item-changed", "true");
    }
  }

  /**
   * @param {number} amount
   */
  function formatCurrency(amount) {
    let formattedAmount;

    try {
      const locale = `${window.Shopify.locale}-${window.Shopify.country}`;

      formattedAmount = amount.toLocaleString(locale, {
        style: "currency",
        currency: window.Shopify.currency.active,
      });
    } catch (_) {
      formattedAmount = amount.toLocaleString(undefined, {
        style: "currency",
        currency: window.Shopify.currency.active,
      });
    }

    return formattedAmount;
  }
})();
