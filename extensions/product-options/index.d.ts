declare interface Window {
  Shopify: any;
  EasyFlowProductOptions: {
    objectToFormData: (data: Record<string, any>, rootKey?: string, formData?: FormData) => FormData;
    formDataToObject: (formData: FormData, initialObject?: Record<string, any>) => FormDataObject;
    parseFetchBody: (fetchArguments: IArguments) => Promise<FormData | string>;
  };
  EasyFlowHelpers: {
    reloadOptionsBlockInitialized: boolean | undefined;
    initialBlock: HTMLElement["outerHTML"] | null;
    getOriginalOptionsBlock: () => HTMLElement["outerHTML"] | null;
    reloadScript: (fetchArguments: string) => void;
    reloadOptionsBlock: () => void;
  };
}

type FormDataValue = FormDataEntryValue | FormDataValue[] | FormDataObject;

interface FormDataObject {
  [index: string]: FormDataValue | FormDataValue[];
}

interface CartItem {
  id: number;
  key: string;
  product_type: string;
  title: string;
  properties: {
    _id?: string;
    _base_product_id?: string;
    _property_name?: string;
    _value_title?: string;
    _value_titles?: string[];
    [key: string]: any;
  };
  variant_id: number;
  quantity: number;
}

interface LegacyCustomizationFields {
  app_embed_placement_selector: string;
  dropdown_placeholder: string;
  is_match_addon_quantity: boolean;
  is_buy_now_integration_disabled: boolean;
  is_enable_remove_addon_from_cart: boolean;
  is_enable_open_addon_product_page: boolean;
  is_change_base_product_price_disabled: boolean;
  is_show_total_price: boolean;
  storefront_footer_text: string;
}
