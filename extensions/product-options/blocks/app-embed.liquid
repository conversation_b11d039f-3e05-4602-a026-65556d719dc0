{% # theme-check-disable AssetSizeAppBlockJavaScript %}
{% schema %}
{
  "name": "EasyFlow Product Options",
  "target": "body",
  "stylesheet": "common.css",
  "javascript": "app-embed.js",
  "settings": []
}
{% endschema %}

{%
  # The code below should be exactly the same as the app-embed.liquid.
  # (Except: id and other data fields)
%}
<style>
  {{ shop.metafields.product_options.styles }}
</style>

<style id="ef__hide-addon-field-styles">
  {{ shop.metafields.product_options.styles }}
</style>

<div
  id='product-options-ho4ed2r6cwo-embed'
  class='ef__product-option-root'
  style='display: none;'

  data-product-base-price='{{ product.price }}'
>
  <form>
    {% liquid
      # Global options assigned by collection, tag, vendor, etc.

      assign options = '' | split: ''
      assign option_ranks = '' | split: ''
      assign rendered_option_ids = ''

      assign rules = '' | split: ''

      for optionSet in shop.metafields.product_options.option_sets.value
        assign is_render_option_set = false

        # Render option if it matches a product property
        if optionSet.productSelectionMethod == 'collection'
          for collection in product.collections
            assign collection_id = collection.id | append: ''

            if optionSet.productSelectionValues contains collection_id
              assign is_render_option_set = true
            endif
          endfor
        elsif optionSet.productSelectionMethod == 'tag'
          for tag in optionSet.productSelectionValues
            if product.tags contains tag
              assign is_render_option_set = true
            endif
          endfor
        elsif optionSet.productSelectionMethod == 'vendor'
          for vendor in optionSet.productSelectionValues
            if product.vendor == vendor
              assign is_render_option_set = true
            endif
          endfor
        endif

        if is_render_option_set
          for option in optionSet.options
            # Prevent rendering one option multiple times
            unless rendered_option_ids contains option.optionId
              assign option_array = option | sort
              assign options = options | concat: option_array

              assign rank_array = optionSet.rank | default: 1 | sort
              assign option_ranks = option_ranks | concat: rank_array

              assign rendered_option_ids = rendered_option_ids | append: option.optionId | append: ','
            endunless
          endfor

          assign rules = rules | concat: optionSet.rules
        endif
      endfor

      assign global_options_size = rendered_option_ids | size

      # Options on the product

      assign rules = rules | concat: product.metafields.product_options.rules.value

      # Get actions that will show selected values (they will be hidden initially)
      assign actions = '' | split: ''

      for rule in rules
        assign actions = actions | concat: rule.actions
      endfor

      assign ranks = product.metafields.product_options.ranks.value

      # theme-check-disable PaginationSize
      paginate product.metafields.product_options.options.value by 250
        if product.metafields.product_options.options.value != blank
          for option in product.metafields.product_options.options.value
            # Prevent rendering one option multiple times
            unless rendered_option_ids contains option.optionId
              assign option_array = option | sort
              assign options = options | concat: option_array

              assign rank_array = ranks[option.optionId] | default: 1 | sort
              assign option_ranks = option_ranks | concat: rank_array

              assign rendered_option_ids = rendered_option_ids | append: option.optionId | append: ','
            endunless
          endfor
        endif
      endpaginate
      # theme-check-enable PaginationSize

      # Render all options sorted by rank
      assign unique_ranks = option_ranks | sort | uniq

      for rank in unique_ranks
        for option in options
          assign option_rank = option_ranks[forloop.index0] | default: 1

          if option_rank == rank
            assign values = option.values.value | default: option.values
            render 'option', option: option, optionValues: values, actions: actions
          endif
        endfor
      endfor
    %}
  </form>

  <p class='ef__option-total-price ef__options-addon-total'>
    {% assign total_text = shop.metafields.product_options.preferences.value.storefrontFooterText
      | default: shop.metafields.product_options.preferences.value.storefront_footer_text
    %}

    {% unless total_text contains '%total-addon-amount%' or total_text contains '%total-amount%' %}
      {% assign total_text = total_text | append: ' %total-addon-amount%' %}
    {% endunless %}

    {{
      total_text
      | default: 'Total add-ons: %total-addon-amount%'
      | replace: '%total-addon-amount%',
        '<span class="ef__options-addon-total-amount ef__addon-amount"></span>'
      | replace: '%total-amount%',
        '<span class="ef__options-addon-total-amount ef__total-amount"></span>'
    }}
  </p>

  {% if app.metafields.product_options.watermark.value %}
    <p>
      Created by
      <a
        href='https://apps.shopify.com/product-options-4'
        target='_blank'
        style='text-decoration: none; color: blue;'
      >
        EasyFlow Product Options
      </a>
    </p>
  {% endif %}
</div>

{% # Rules %}
<script
  type='application/json'
  id='ef__product-option-rules'

  {% if global_options_size > 0 %}
    data-is-includes-global='true'
  {% endif %}
>
  {{ rules | json }}
</script>

{% # Variant data %}
{% liquid
  assign is_includes_shopify_rules = false

  for rule in rules
    for condition in rule.conditions
      if condition.isShopifyOption
        assign is_includes_shopify_rules = true
        break
      endif
    endfor
  endfor
%}

{% if is_includes_shopify_rules
  or shop.metafields.product_options.preferences.value.isChangeBaseProductPriceDisabled == false
%}
  <script type='application/json' id='ef__product-variant-data'>
    {
      "options": {{ product.options | json }},
      "variants": [{% for variant in product.variants %}{
        "id": {{ variant.id | json }},
        "options": {{ variant.options | json }},
        "available": {{ variant.available | json }},
        "price": {{ variant.price | json }},
        "compare_at_price": {{ variant.compare_at_price | json }}
      }{% unless forloop.last %},{% endunless %}{% endfor %}]
    }
  </script>
{% endif %}

{% # User preferences %}
<script type='application/json' id='ef__product-option-customizations'>
  {% if shop.metafields.product_options.preferences.value %}
    {{ shop.metafields.product_options.preferences.value | json }}
  {% else %}
    {}
  {% endif %}
</script>

{% # theme-check-disable ParserBlockingScript %}
<script src='{{- 'public.js' | asset_url -}}'></script>
{% # theme-check-enable ParserBlockingScript %}

<script src='{{- 'common.js' | asset_url -}}' async></script>

<div id="easyflow-common-js-url" data-common-url="{{ 'common.js' | asset_url }}"></div>
